# MDEdit - Modern Markdown Editor

A modern, responsive markdown editor with live preview built with React, TypeScript, and Tailwind CSS.

## Features

- **Drag & Drop File Support**: Simply drag markdown files into the editor
- **Live Preview**: Real-time markdown rendering with GitHub Flavored Markdown support
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Dark Mode**: Toggle between light and dark themes
- **Progressive Web App**: Installable with offline capabilities
- **Epic-based Architecture**: Scalable architecture that evolves with features

## Tech Stack

- **Frontend**: React 18+ with TypeScript 5.0+
- **Build Tool**: Vite 7.0+ for fast development and optimized builds
- **Styling**: Tailwind CSS 3.3+ for utility-first styling
- **State Management**: Zustand 4.4+ for lightweight state management
- **Markdown Parser**: Marked.js 9.0+ with GitHub Flavored Markdown
- **Testing**: Vitest for unit testing, Playwright for E2E testing
- **Deployment**: Vercel with GitHub Actions CI/CD

## Getting Started

### Prerequisites

- Node.js 18.x or 20.x
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mdedit
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

### Building for Production

```bash
npm run build
```

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Linting
npm run lint

# Format code
npm run format
```

## Project Structure

```
src/
├── components/
│   ├── ui/           # Base UI components
│   ├── editor/       # Editor components
│   ├── preview/      # Preview components
│   ├── layout/       # Layout components
│   └── file/         # File handling components
├── hooks/            # Custom React hooks
├── services/         # Business logic services
├── types/            # TypeScript definitions
├── utils/            # Utility functions
└── styles/           # Global styles
```

## Epic Architecture

This project follows an epic-based architecture that evolves progressively:

- **Epic 1**: Basic textarea editor with live preview (Current)
- **Epic 2**: Enhanced editor with syntax highlighting
- **Epic 3**: Advanced CodeMirror integration
- **Epic 4**: Full PWA with offline capabilities

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details


