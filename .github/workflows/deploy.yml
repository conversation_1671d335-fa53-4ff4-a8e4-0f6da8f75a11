name: Deploy to Vercel

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npx tsc --noEmit

    - name: Run tests
      run: npm run test

    - name: Build application
      run: npm run build

    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        github-token: ${{ secrets.GITHUB_TOKEN }}
        vercel-args: '--prod'
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        working-directory: ./

  performance-check:
    runs-on: ubuntu-latest
    needs: deploy

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Performance audit
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          https://mdedit.vercel.app
        budgetPath: .lighthouserc.json
        uploadArtifacts: true
        temporaryPublicStorage: true
