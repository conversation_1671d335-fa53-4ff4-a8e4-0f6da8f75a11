# <!-- Powered by BMAD™ Core -->

# Story 1.5: File Download and Save Functionality

## Status
Draft

## Story
**As a** user,
**I want** to download my edited markdown content as a file,
**so that** I can save my changes and use the content elsewhere.

## Acceptance Criteria
1. Download button triggers file save using browser download functionality
2. Downloaded file maintains original filename with .md extension
3. File content preserves exact formatting and character encoding from editor
4. Download works across all supported browsers without additional plugins
5. File MIME type set correctly for markdown content
6. Download filename handling supports special characters and spaces appropriately
7. Large files (up to 200KB) download successfully without truncation
8. Downloaded content can be re-uploaded to verify round-trip integrity

## Tasks / Subtasks

- [ ] Create download functionality service (AC: 1, 4)
  - [ ] Implement FileDownloader utility in src/utils/file-utils.ts
  - [ ] Use browser's download API (document.createElement + click)
  - [ ] Handle cross-browser compatibility for download behavior
  - [ ] Create downloadFile function with proper error handling

- [ ] Implement filename handling logic (AC: 2, 6)
  - [ ] Preserve original filename when available
  - [ ] Ensure .md extension is always present
  - [ ] Sanitize filenames for cross-platform compatibility
  - [ ] Handle special characters, spaces, and Unicode in filenames
  - [ ] Implement fallback naming for files without original names

- [ ] Configure file encoding and MIME type (AC: 3, 5)
  - [ ] Set correct MIME type: text/markdown or text/plain
  - [ ] Preserve UTF-8 encoding throughout download process
  - [ ] Handle BOM (Byte Order Mark) appropriately
  - [ ] Test with files containing special characters and emojis

- [ ] Create download UI component (AC: 1)
  - [ ] Add Download button to Header or StatusBar component
  - [ ] Implement loading state during file preparation
  - [ ] Add success feedback after download initiation
  - [ ] Ensure button is accessible and keyboard navigable

- [ ] Handle large file downloads (AC: 7)
  - [ ] Test download performance with 200KB files
  - [ ] Implement progress indication for large files if needed
  - [ ] Handle browser memory limitations gracefully
  - [ ] Optimize blob creation for large content

- [ ] Verify round-trip integrity (AC: 8)
  - [ ] Create test suite for upload-edit-download workflow
  - [ ] Verify downloaded content matches edited content exactly
  - [ ] Test character encoding preservation
  - [ ] Validate that downloaded files can be re-uploaded successfully

## Dev Notes

### Previous Story Insights
Story 1.4 implemented the preview functionality. This story completes the core user workflow by allowing users to save their edited content, creating a complete edit-and-save cycle.

### Project Structure Requirements
[Source: architecture/project-structure.md]
- Add download utilities to `src/utils/file-utils.ts`
- Update Header component in `src/components/layout/Header.tsx`
- Add download button to StatusBar if applicable
- Create download-related types in `src/types/file.ts`

### Browser Download Implementation
```typescript
// File Download Utility
interface DownloadOptions {
  filename: string;
  mimeType: string;
  encoding: string;
}

const downloadFile = (content: string, options: DownloadOptions) => {
  // Create blob with proper encoding
  const blob = new Blob([content], { 
    type: `${options.mimeType};charset=${options.encoding}` 
  });
  
  // Create download link
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = options.filename;
  
  // Trigger download
  document.body.appendChild(link);
  link.click();
  
  // Cleanup
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
```

### Filename Sanitization Logic
```typescript
// Filename Sanitization for Cross-Platform Compatibility
const sanitizeFilename = (filename: string): string => {
  // Remove or replace invalid characters for Windows/Mac/Linux
  const sanitized = filename
    .replace(/[<>:"/\\|?*]/g, '_')  // Invalid Windows chars
    .replace(/^\.+/, '_')           // Leading dots
    .replace(/\.+$/, '')            // Trailing dots
    .replace(/\s+/g, '_')           // Multiple spaces to single underscore
    .substring(0, 255);             // Length limit
  
  // Ensure .md extension
  if (!sanitized.toLowerCase().endsWith('.md')) {
    return `${sanitized}.md`;
  }
  
  return sanitized;
};

const generateDownloadFilename = (originalName?: string): string => {
  if (originalName) {
    return sanitizeFilename(originalName);
  }
  
  // Fallback naming with timestamp
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  return `markdown-${timestamp}.md`;
};
```

### MIME Type Configuration
[Source: architecture/tech-stack.md]
- **Primary MIME Type:** `text/markdown`
- **Fallback MIME Type:** `text/plain` for broader compatibility
- **Character Encoding:** UTF-8
- **BOM Handling:** No BOM for UTF-8 files (standard practice)

### Cross-Browser Compatibility Requirements
[Source: architecture/tech-stack.md]
- **Chrome 90+:** Native download attribute support
- **Firefox 88+:** Full blob download support  
- **Safari 14+:** Webkit download handling
- **Edge 90+:** Chromium-based download behavior

Test scenarios for each browser:
- Standard ASCII filenames
- Unicode filenames with special characters
- Large file downloads (approaching 200KB)
- Files with various markdown content types

### State Management Integration
```typescript
// Download State Management
interface DownloadState {
  isDownloading: boolean;
  lastDownloadTime: Date | null;
  error: string | null;
  downloadFile: (content: string, filename?: string) => Promise<void>;
}

// Usage in component
const handleDownload = async () => {
  if (!currentFile) return;
  
  try {
    setIsDownloading(true);
    await downloadFile(editorContent, currentFile.filename);
    setLastDownloadTime(new Date());
  } catch (error) {
    setError(`Download failed: ${error.message}`);
  } finally {
    setIsDownloading(false);
  }
};
```

### UI/UX Requirements
- **Download Button Location:** Header toolbar or StatusBar
- **Visual Design:** Clear download icon with text label
- **Loading State:** Show spinner/progress during file preparation
- **Success Feedback:** Brief notification or animation
- **Error Handling:** Clear error messages for failures
- **Keyboard Access:** Tab navigation and Enter/Space activation

### Performance Considerations
[Source: architecture/performance-and-deployment-strategy.md]
- File preparation should complete within 500ms for files up to 50KB
- Large files (50-200KB) may show progress indication
- Memory efficient blob creation to avoid browser crashes
- Cleanup URLs promptly to prevent memory leaks

### Error Scenarios and Handling
```typescript
// Common Download Error Scenarios
enum DownloadError {
  CONTENT_EMPTY = 'Cannot download empty content',
  FILENAME_INVALID = 'Invalid filename provided',
  BROWSER_BLOCKED = 'Browser blocked download (popup blocker)',
  CONTENT_TOO_LARGE = 'File content exceeds browser limits',
  ENCODING_ERROR = 'Character encoding error during preparation'
}
```

### Security Considerations
- Sanitize filenames to prevent directory traversal
- Validate content doesn't contain executable code markers
- Use proper MIME types to prevent browser misinterpretation
- Implement size limits to prevent memory exhaustion attacks

### Accessibility Requirements
- Download button must be keyboard accessible
- Screen reader compatible with proper ARIA labels
- High contrast focus indicators
- Clear success/error announcements for screen readers

### Testing Strategy for Round-Trip Integrity
```typescript
// Round-Trip Test Scenarios
const roundTripTests = [
  'Basic markdown with headers and paragraphs',
  'Code blocks with various programming languages',
  'Unicode content with emojis and special characters',
  'Large files approaching 200KB limit',
  'Files with Windows/Unix line endings',
  'Content with HTML entities and markdown escapes'
];
```

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test download utility functions and filename sanitization
- **Integration Tests:** Test download button integration with editor state
- **E2E Tests:** Use Playwright to simulate download workflows
- **Cross-browser Tests:** Verify download behavior in all supported browsers

### Key Test Scenarios
1. Basic file download with standard filename
2. Download with special characters in filename
3. Large file download (approaching 200KB)
4. Download without original filename (fallback naming)
5. Character encoding preservation through download
6. Round-trip integrity (upload → edit → download → verify)
7. Download button accessibility and keyboard navigation
8. Error handling for various failure scenarios

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled during development*

### Debug Log References
*To be filled during development*

### Completion Notes List
*To be filled during development*

### File List
*To be filled during development*

## QA Results
*Results from QA Agent review will be populated here after story completion*