# <!-- Powered by BMAD™ Core -->

# Story 2.2: Real-Time Preview Synchronization

## Status
Draft

## Story
**As a** user,
**I want** the preview to update instantly as I type,
**so that** I can see formatting results immediately without manual refresh.

## Acceptance Criteria
1. Preview updates automatically within 100ms of keystroke activity
2. Debouncing prevents excessive rendering during rapid typing
3. Incremental parsing minimizes computation for small changes
4. Cursor position and editor focus maintained during preview updates
5. Preview rendering does not block editor typing or scrolling
6. Large documents (100KB+) maintain real-time updates without UI freezing
7. Error states in markdown syntax display helpful feedback in preview
8. Real-time updates can be toggled on/off for performance-sensitive environments

## Tasks / Subtasks

- [ ] Create real-time update system architecture (AC: 1, 2)
  - [ ] Implement PreviewSyncManager service in src/services/
  - [ ] Create debounced update mechanism with 100ms delay
  - [ ] Design update pipeline from editor changes to preview rendering
  - [ ] Implement performance monitoring for update latency

- [ ] Implement incremental parsing optimization (AC: 3)
  - [ ] Analyze document changes to identify modified sections
  - [ ] Implement differential parsing for unchanged content
  - [ ] Cache parsed results for stable document sections
  - [ ] Optimize re-rendering to minimize computational overhead

- [ ] Maintain editor state during updates (AC: 4, 5)
  - [ ] Preserve cursor position during preview updates
  - [ ] Prevent editor focus loss during rendering operations
  - [ ] Ensure typing and scrolling remain responsive
  - [ ] Implement non-blocking preview update architecture

- [ ] Optimize large document performance (AC: 6)
  - [ ] Test real-time updates with documents up to 200KB
  - [ ] Implement chunked processing for large documents
  - [ ] Add performance monitoring and automatic degradation
  - [ ] Ensure UI responsiveness during heavy processing

- [ ] Create error handling and feedback system (AC: 7)
  - [ ] Detect and display markdown syntax errors in preview
  - [ ] Provide helpful error messages for malformed syntax
  - [ ] Implement graceful fallback for parsing failures
  - [ ] Show error indicators without breaking the editing flow

- [ ] Add performance preference controls (AC: 8)
  - [ ] Create toggle for enabling/disabling real-time updates
  - [ ] Implement manual update trigger as fallback option
  - [ ] Add performance mode selection (fast, balanced, quality)
  - [ ] Store user preferences for update behavior

- [ ] Integrate with CodeMirror editor from Story 2.1 (AC: 1, 4)
  - [ ] Connect real-time updates to CodeMirror change events
  - [ ] Ensure compatibility with CodeMirror's document model
  - [ ] Handle CodeMirror-specific editor states and events
  - [ ] Test integration with syntax highlighting updates

## Dev Notes

### Previous Story Insights
Story 2.1 implemented CodeMirror integration with professional editing features. This story builds on that foundation by adding real-time preview synchronization, creating a seamless editing experience where users see immediate visual feedback.

### Project Structure Requirements
[Source: architecture/project-structure.md]
- Create sync management in `src/services/`:
  - `PreviewSyncManager.ts` - Core synchronization logic
  - `MarkdownParser.ts` - Enhanced with incremental parsing
  - `PerformanceMonitor.ts` - Update performance tracking
- Update preview components in `src/components/preview/`:
  - Enhance `PreviewPane.tsx` with real-time capabilities
  - Update `MarkdownRenderer.tsx` for efficient re-rendering

### Epic 2 Real-Time Architecture
[Source: architecture/epic-based-architecture-evolution.md]
```typescript
// Debounced Update Manager
class PreviewSyncManager {
  private debounceTimer: NodeJS.Timeout | null = null;
  private readonly DEBOUNCE_DELAY = 100; // ms
  private lastContent: string = '';
  private contentCache: Map<string, string> = new Map();
  
  scheduleUpdate(content: string, callback: (html: string) => void) {
    if (this.debounceTimer) clearTimeout(this.debounceTimer);
    
    this.debounceTimer = setTimeout(() => {
      this.processContentUpdate(content, callback);
    }, this.DEBOUNCE_DELAY);
  }
  
  private processContentUpdate(content: string, callback: (html: string) => void) {
    // Check cache first
    const cached = this.contentCache.get(content);
    if (cached) {
      callback(cached);
      return;
    }
    
    if (content.length >= 51200) { // 50KB threshold
      this.processWithWorker(content, callback);
    } else {
      this.processOnMainThread(content, callback);
    }
  }
  
  private processOnMainThread(content: string, callback: (html: string) => void) {
    try {
      const startTime = performance.now();
      const html = marked(content, { gfm: true });
      const duration = performance.now() - startTime;
      
      // Cache result
      this.contentCache.set(content, html);
      
      // Monitor performance
      PerformanceMonitor.trackPreviewUpdate(content.length, duration);
      
      callback(html);
    } catch (error) {
      this.handleParsingError(error, callback);
    }
  }
}
```

### Incremental Parsing Strategy
```typescript
// Incremental Parsing Implementation
class IncrementalParser {
  private documentSections: Map<string, ParsedSection> = new Map();
  
  parseWithDiff(newContent: string, oldContent: string): string {
    const changes = this.detectChanges(newContent, oldContent);
    
    if (changes.length === 0) {
      // No changes, return cached result
      return this.getCachedResult(newContent);
    }
    
    // Process only changed sections
    const updatedSections = this.updateChangedSections(changes);
    const fullHtml = this.reassembleDocument(updatedSections);
    
    return fullHtml;
  }
  
  private detectChanges(newContent: string, oldContent: string): Change[] {
    // Implement efficient diff algorithm
    // Split into sections (headers, paragraphs, code blocks)
    // Identify which sections have changed
    return this.performDiff(newContent, oldContent);
  }
  
  private updateChangedSections(changes: Change[]): ParsedSection[] {
    return changes.map(change => {
      const parsed = marked(change.content, { gfm: true });
      return {
        id: change.id,
        html: parsed,
        startLine: change.startLine,
        endLine: change.endLine
      };
    });
  }
}
```

### Performance Monitoring System
[Source: architecture/performance-and-deployment-strategy.md]
```typescript
// Performance Monitoring for Real-Time Updates
class PerformanceMonitor {
  private static updateTimes: number[] = [];
  private static readonly MAX_SAMPLES = 100;
  
  static trackPreviewUpdate(contentSize: number, duration: number) {
    this.updateTimes.push(duration);
    
    // Keep only recent samples
    if (this.updateTimes.length > this.MAX_SAMPLES) {
      this.updateTimes = this.updateTimes.slice(-this.MAX_SAMPLES);
    }
    
    // Alert if performance degrades
    const avgTime = this.getAverageUpdateTime();
    if (avgTime > 100 && duration > 200) {
      console.warn(`Preview update performance degraded: ${duration}ms (avg: ${avgTime}ms)`);
      this.recommendPerformanceMode(contentSize);
    }
    
    // Track metrics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'preview_update_time', {
        custom_map: { 
          metric1: 'content_size', 
          metric2: 'update_duration',
          metric3: 'average_time'
        },
        metric1: contentSize,
        metric2: duration,
        metric3: avgTime
      });
    }
  }
  
  private static recommendPerformanceMode(contentSize: number) {
    if (contentSize > 100000) { // 100KB
      // Suggest Web Worker mode or reduced update frequency
      this.notifyPerformanceRecommendation('large_document');
    }
  }
}
```

### Error Handling and User Feedback
```typescript
// Error Handling for Real-Time Updates
interface ParseError {
  line: number;
  column: number;
  message: string;
  type: 'syntax' | 'performance' | 'unknown';
}

class ErrorHandler {
  static handleParsingError(error: any, content: string): ParseError {
    // Attempt to extract line/column information
    const lineInfo = this.extractLineInfo(error, content);
    
    return {
      line: lineInfo.line,
      column: lineInfo.column,
      message: this.createUserFriendlyMessage(error),
      type: this.categorizeError(error)
    };
  }
  
  private static createUserFriendlyMessage(error: any): string {
    if (error.message.includes('Invalid markdown')) {
      return 'Invalid markdown syntax detected. Check your formatting.';
    }
    if (error.message.includes('Performance')) {
      return 'Document is large and may affect performance. Consider enabling performance mode.';
    }
    return 'An error occurred while processing your markdown. Please check your syntax.';
  }
  
  static displayError(error: ParseError, previewElement: HTMLElement) {
    const errorDisplay = document.createElement('div');
    errorDisplay.className = 'preview-error bg-red-50 border border-red-200 rounded p-4 mb-4';
    errorDisplay.innerHTML = `
      <div class="flex items-start">
        <div class="text-red-500 mr-2">⚠️</div>
        <div>
          <p class="text-red-800 font-medium">${error.message}</p>
          ${error.line > 0 ? `<p class="text-red-600 text-sm">Line ${error.line}${error.column > 0 ? `, Column ${error.column}` : ''}</p>` : ''}
        </div>
      </div>
    `;
    
    previewElement.prepend(errorDisplay);
  }
}
```

### User Preference Controls
```typescript
// Real-Time Update Preferences
interface PreviewPreferences {
  realTimeUpdates: boolean;
  updateDelay: number; // milliseconds
  performanceMode: 'fast' | 'balanced' | 'quality';
  showErrors: boolean;
  maxDocumentSize: number; // KB threshold
}

const usePreviewPreferences = () => {
  const [preferences, setPreferences] = useState<PreviewPreferences>({
    realTimeUpdates: true,
    updateDelay: 100,
    performanceMode: 'balanced',
    showErrors: true,
    maxDocumentSize: 100
  });

  const updatePreference = <K extends keyof PreviewPreferences>(
    key: K,
    value: PreviewPreferences[K]
  ) => {
    const updated = { ...preferences, [key]: value };
    setPreferences(updated);
    localStorage.setItem('preview-preferences', JSON.stringify(updated));
  };

  return { preferences, updatePreference };
};
```

### Integration with CodeMirror Events
```typescript
// CodeMirror Integration for Real-Time Updates
import { EditorView, ViewUpdate } from '@codemirror/view';

const createUpdateListener = (syncManager: PreviewSyncManager) => {
  return EditorView.updateListener.of((update: ViewUpdate) => {
    if (update.docChanged) {
      const content = update.state.doc.toString();
      const cursor = update.state.selection.main.head;
      
      // Schedule preview update while preserving cursor
      syncManager.scheduleUpdate(content, (html) => {
        // Update preview without affecting editor focus
        updatePreview(html);
        
        // Ensure cursor position is maintained
        if (update.view.hasFocus) {
          update.view.focus();
          // Restore cursor position if needed
        }
      });
    }
  });
};
```

### Performance Optimization Strategies
- **Debouncing:** 100ms delay prevents excessive updates during rapid typing
- **Caching:** Store parsed results for unchanged content
- **Incremental Parsing:** Process only modified document sections
- **Chunked Processing:** Handle large documents in manageable pieces
- **Performance Monitoring:** Track update times and recommend optimizations

### Memory Management
```typescript
// Memory Management for Real-Time Updates
class PreviewMemoryManager {
  private static readonly MAX_CACHE_SIZE = 50; // entries
  private static readonly CACHE_CLEANUP_INTERVAL = 60000; // 1 minute
  
  static setupMemoryManagement(syncManager: PreviewSyncManager) {
    // Periodic cache cleanup
    setInterval(() => {
      this.cleanupCache(syncManager);
    }, this.CACHE_CLEANUP_INTERVAL);
    
    // Monitor memory usage
    this.monitorMemoryUsage();
  }
  
  private static cleanupCache(syncManager: PreviewSyncManager) {
    // Remove oldest cache entries if over limit
    const cache = syncManager.getCache();
    if (cache.size > this.MAX_CACHE_SIZE) {
      const entries = Array.from(cache.entries());
      const toRemove = entries.slice(0, entries.length - this.MAX_CACHE_SIZE);
      toRemove.forEach(([key]) => cache.delete(key));
    }
  }
}
```

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test PreviewSyncManager and debouncing logic
- **Performance Tests:** Verify 100ms update latency and large document handling
- **Integration Tests:** Test CodeMirror integration and editor state preservation
- **E2E Tests:** Test real-time editing experience with various content types

### Key Test Scenarios
1. Preview updates within 100ms of typing cessation
2. Debouncing prevents excessive updates during rapid typing
3. Editor focus and cursor position maintained during updates
4. Large documents (100KB+) maintain smooth real-time updates
5. Error handling displays helpful feedback for syntax issues
6. Performance toggle correctly enables/disables real-time updates
7. Incremental parsing optimizes performance for small changes
8. Memory usage remains stable during extended real-time editing

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled during development*

### Debug Log References
*To be filled during development*

### Completion Notes List
*To be filled during development*

### File List
*To be filled during development*

## QA Results
*Results from QA Agent review will be populated here after story completion*