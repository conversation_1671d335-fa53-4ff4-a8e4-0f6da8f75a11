# <!-- Powered by BMAD™ Core -->

# Story 4.2: Progressive Web App Implementation

## Status
Draft

## Story
**As a** user,
**I want** to install MDEdit as a native-like app on my devices,
**so that** I can access it quickly and work with it like a desktop application.

## Acceptance Criteria
1. Service worker registration enables PWA installation across supported browsers
2. Web app manifest provides proper app metadata, icons, and display settings
3. Installable on desktop (Chrome, Edge) and mobile (Android, iOS Safari) platforms
4. App icon and splash screen display consistently across different devices
5. Standalone display mode provides native app experience without browser chrome
6. App shortcuts in manifest enable quick access to common functions
7. Background sync capability for future online/offline coordination features
8. Push notification infrastructure prepared for future feature expansion
9. PWA installation prompts display appropriately without being intrusive

## Tasks / Subtasks

- [ ] Create and configure service worker (AC: 1, 7)
  - [ ] Implement comprehensive service worker in public/sw.js
  - [ ] Add caching strategies for app shell and dynamic content
  - [ ] Implement background sync for offline capabilities
  - [ ] Add service worker update mechanisms

- [ ] Create web app manifest (AC: 2, 4, 5, 6)
  - [ ] Design and generate PWA icons in multiple sizes
  - [ ] Configure manifest.json with proper metadata
  - [ ] Set up standalone display mode and theme colors
  - [ ] Add app shortcuts for common actions

- [ ] Implement PWA installation logic (AC: 3, 9)
  - [ ] Create PWAManager service for installation handling
  - [ ] Add installation prompt management
  - [ ] Handle installation across different browsers and platforms
  - [ ] Create user-friendly installation UI

- [ ] Design app icons and splash screens (AC: 4)
  - [ ] Create high-quality app icons for all required sizes
  - [ ] Generate splash screens for different screen sizes
  - [ ] Ensure consistent branding across all platforms
  - [ ] Test icon display across various devices

- [ ] Set up caching strategies (AC: 1)
  - [ ] Implement app shell caching for instant loading
  - [ ] Add runtime caching for dynamic content
  - [ ] Create cache management and cleanup strategies
  - [ ] Handle cache versioning and updates

- [ ] Create PWA features infrastructure (AC: 7, 8)
  - [ ] Set up background sync registration
  - [ ] Prepare push notification service integration
  - [ ] Add indexedDB integration for offline data
  - [ ] Create update notification system

- [ ] Test PWA functionality across platforms (AC: 3, 4, 5)
  - [ ] Test installation on Chrome, Edge, Safari, Firefox
  - [ ] Verify standalone mode functionality
  - [ ] Test app shortcuts and navigation
  - [ ] Validate icon and splash screen display

## Dev Notes

### Service Worker Implementation
```typescript
// Comprehensive Service Worker (public/sw.js)
const CACHE_NAME = 'mdedit-v1.0.0';
const STATIC_CACHE = 'mdedit-static-v1';
const DYNAMIC_CACHE = 'mdedit-dynamic-v1';

// Files to cache for app shell
const STATIC_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/static/js/main.js',
  '/static/css/main.css',
  '/static/media/icons/icon-192x192.png',
  '/static/media/icons/icon-512x512.png',
  '/offline.html' // Fallback page
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        // Skip waiting to activate immediately
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        // Claim clients immediately
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }
  
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          // Return cached version
          return cachedResponse;
        }
        
        // Fetch from network
        return fetch(event.request)
          .then((response) => {
            // Don't cache if not successful
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }
            
            // Cache dynamic content
            const responseClone = response.clone();
            caches.open(DYNAMIC_CACHE)
              .then((cache) => {
                cache.put(event.request, responseClone);
              });
            
            return response;
          })
          .catch(() => {
            // Return offline fallback for navigation requests
            if (event.request.mode === 'navigate') {
              return caches.match('/offline.html');
            }
          });
      })
  );
});

// Background sync for future offline features
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync:', event.tag);
  
  if (event.tag === 'background-save') {
    event.waitUntil(
      syncPendingSaves()
    );
  }
});

// Push notifications for future features
self.addEventListener('push', (event) => {
  console.log('[SW] Push received:', event);
  
  const options = {
    body: event.data?.text() || 'New update available',
    icon: '/static/media/icons/icon-192x192.png',
    badge: '/static/media/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: '2'
    },
    actions: [
      {
        action: 'explore',
        title: 'Open MDEdit',
        icon: '/static/media/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/static/media/icons/xmark.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('MDEdit', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});
```

### Web App Manifest
```json
{
  "name": "MDEdit - Markdown Editor",
  "short_name": "MDEdit",
  "description": "Professional markdown editor with real-time preview and advanced features",
  "start_url": "/",
  "display": "standalone",
  "orientation": "any",
  "theme_color": "#1f6feb",
  "background_color": "#ffffff",
  "lang": "en",
  "scope": "/",
  "categories": ["productivity", "utilities"],
  "icons": [
    {
      "src": "/static/media/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/static/media/icons/icon-maskable-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable"
    },
    {
      "src": "/static/media/icons/icon-maskable-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable"
    }
  ],
  "screenshots": [
    {
      "src": "/static/media/screenshots/desktop-wide.png",
      "sizes": "1280x720",
      "type": "image/png",
      "form_factor": "wide",
      "label": "MDEdit editing interface on desktop"
    },
    {
      "src": "/static/media/screenshots/mobile-narrow.png",
      "sizes": "390x844",
      "type": "image/png",
      "form_factor": "narrow",
      "label": "MDEdit mobile interface"
    }
  ],
  "shortcuts": [
    {
      "name": "New Document",
      "short_name": "New",
      "description": "Create a new markdown document",
      "url": "/?action=new",
      "icons": [
        {
          "src": "/static/media/icons/shortcut-new.png",
          "sizes": "192x192",
          "type": "image/png"
        }
      ]
    },
    {
      "name": "Open File",
      "short_name": "Open",
      "description": "Open an existing markdown file",
      "url": "/?action=open",
      "icons": [
        {
          "src": "/static/media/icons/shortcut-open.png",
          "sizes": "192x192",
          "type": "image/png"
        }
      ]
    },
    {
      "name": "Settings",
      "short_name": "Settings",
      "description": "Configure MDEdit preferences",
      "url": "/?action=settings",
      "icons": [
        {
          "src": "/static/media/icons/shortcut-settings.png",
          "sizes": "192x192",
          "type": "image/png"
        }
      ]
    }
  ],
  "file_handlers": [
    {
      "action": "/",
      "accept": {
        "text/markdown": [".md", ".markdown"],
        "text/plain": [".txt"]
      }
    }
  ],
  "edge_side_panel": {
    "preferred_width": 400
  },
  "handle_links": "preferred",
  "launch_handler": {
    "client_mode": "focus-existing"
  }
}
```

### PWA Manager Service
```typescript
// PWA Installation and Management
interface PWAInstallEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
  prompt(): Promise<void>;
}

interface PWAStatus {
  isInstallable: boolean;
  isInstalled: boolean;
  platform: string;
  displayMode: string;
}

class PWAManager {
  private deferredPrompt: PWAInstallEvent | null = null;
  private installationListeners: ((status: PWAStatus) => void)[] = [];
  
  constructor() {
    this.setupEventListeners();
    this.detectPWAStatus();
  }
  
  private setupEventListeners(): void {
    // Listen for install prompt
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA install prompt available');
      e.preventDefault();
      this.deferredPrompt = e as PWAInstallEvent;
      this.notifyStatusChange();
    });
    
    // Listen for app installation
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      this.deferredPrompt = null;
      this.notifyStatusChange();
    });
    
    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    mediaQuery.addEventListener('change', () => {
      this.notifyStatusChange();
    });
  }
  
  private detectPWAStatus(): void {
    // Detect if already installed
    const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                       (window.navigator as any).standalone === true ||
                       document.referrer.includes('android-app://');
    
    if (isInstalled) {
      console.log('PWA is already installed');
    }
    
    this.notifyStatusChange();
  }
  
  async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.log('No install prompt available');
      return false;
    }
    
    try {
      // Show the install prompt
      await this.deferredPrompt.prompt();
      
      // Wait for the user's choice
      const choiceResult = await this.deferredPrompt.userChoice;
      
      console.log('User choice:', choiceResult.outcome);
      
      // Clean up
      this.deferredPrompt = null;
      
      return choiceResult.outcome === 'accepted';
    } catch (error) {
      console.error('Install prompt failed:', error);
      return false;
    }
  }
  
  getPWAStatus(): PWAStatus {
    const displayMode = this.getDisplayMode();
    
    return {
      isInstallable: !!this.deferredPrompt,
      isInstalled: displayMode === 'standalone',
      platform: this.detectPlatform(),
      displayMode
    };
  }
  
  private getDisplayMode(): string {
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return 'standalone';
    }
    if (window.matchMedia('(display-mode: minimal-ui)').matches) {
      return 'minimal-ui';
    }
    if (window.matchMedia('(display-mode: fullscreen)').matches) {
      return 'fullscreen';
    }
    return 'browser';
  }
  
  private detectPlatform(): string {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/android/.test(userAgent)) return 'android';
    if (/iphone|ipad/.test(userAgent)) return 'ios';
    if (/windows/.test(userAgent)) return 'windows';
    if (/macintosh/.test(userAgent)) return 'macos';
    if (/linux/.test(userAgent)) return 'linux';
    
    return 'unknown';
  }
  
  private notifyStatusChange(): void {
    const status = this.getPWAStatus();
    this.installationListeners.forEach(listener => listener(status));
  }
  
  onStatusChange(listener: (status: PWAStatus) => void): () => void {
    this.installationListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.installationListeners.indexOf(listener);
      if (index > -1) {
        this.installationListeners.splice(index, 1);
      }
    };
  }
}
```

### PWA Installation UI Component
```typescript
// PWA Installation Prompt Component
const PWAInstallPrompt: React.FC = () => {
  const [pwaStatus, setPwaStatus] = useState<PWAStatus>({
    isInstallable: false,
    isInstalled: false,
    platform: 'unknown',
    displayMode: 'browser'
  });
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  
  useEffect(() => {
    const pwaManager = new PWAManager();
    
    // Initial status
    setPwaStatus(pwaManager.getPWAStatus());
    
    // Listen for status changes
    const unsubscribe = pwaManager.onStatusChange((status) => {
      setPwaStatus(status);
      
      // Show prompt if installable and not already shown
      if (status.isInstallable && !status.isInstalled && !localStorage.getItem('pwa-prompt-dismissed')) {
        // Delay showing prompt to avoid interrupting user
        setTimeout(() => setShowPrompt(true), 2000);
      }
    });
    
    return unsubscribe;
  }, []);
  
  const handleInstall = async () => {
    setIsInstalling(true);
    
    try {
      const pwaManager = new PWAManager();
      const success = await pwaManager.promptInstall();
      
      if (success) {
        setShowPrompt(false);
      }
    } catch (error) {
      console.error('Installation failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };
  
  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem('pwa-prompt-dismissed', 'true');
  };
  
  if (!showPrompt || pwaStatus.isInstalled || !pwaStatus.isInstallable) {
    return null;
  }
  
  return (
    <div className="pwa-install-prompt">
      <div className="prompt-content">
        <div className="prompt-icon">
          <img src="/static/media/icons/icon-96x96.png" alt="MDEdit" />
        </div>
        <div className="prompt-text">
          <h3>Install MDEdit</h3>
          <p>Add MDEdit to your {pwaStatus.platform === 'ios' ? 'home screen' : 'desktop'} for quick access</p>
        </div>
        <div className="prompt-actions">
          <button 
            onClick={handleDismiss}
            className="prompt-button secondary"
          >
            Not now
          </button>
          <button 
            onClick={handleInstall}
            className="prompt-button primary"
            disabled={isInstalling}
          >
            {isInstalling ? 'Installing...' : 'Install'}
          </button>
        </div>
      </div>
    </div>
  );
};

// PWA Status Indicator
const PWAStatusIndicator: React.FC = () => {
  const [pwaStatus, setPwaStatus] = useState<PWAStatus | null>(null);
  
  useEffect(() => {
    const pwaManager = new PWAManager();
    setPwaStatus(pwaManager.getPWAStatus());
    
    const unsubscribe = pwaManager.onStatusChange(setPwaStatus);
    return unsubscribe;
  }, []);
  
  if (!pwaStatus) return null;
  
  return (
    <div className={`pwa-status-indicator ${pwaStatus.displayMode}`}>
      {pwaStatus.isInstalled ? (
        <div className="status-item installed">
          <span className="icon">✅</span>
          <span>App Mode</span>
        </div>
      ) : pwaStatus.isInstallable ? (
        <div className="status-item installable">
          <span className="icon">📱</span>
          <span>Installable</span>
        </div>
      ) : (
        <div className="status-item browser">
          <span className="icon">🌐</span>
          <span>Browser</span>
        </div>
      )}
    </div>
  );
};
```

### Caching Strategy Implementation
```typescript
// Advanced Caching Strategies
interface CacheConfig {
  name: string;
  strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
  maxEntries?: number;
  maxAge?: number; // seconds
}

class CacheManager {
  private cacheConfigs: Map<string, CacheConfig> = new Map();
  
  constructor() {
    this.setupCacheStrategies();
  }
  
  private setupCacheStrategies(): void {
    // App shell - cache first
    this.cacheConfigs.set('app-shell', {
      name: 'mdedit-app-shell',
      strategy: 'cache-first',
      maxEntries: 50,
      maxAge: 365 * 24 * 60 * 60 // 1 year
    });
    
    // Static assets - cache first
    this.cacheConfigs.set('static', {
      name: 'mdedit-static',
      strategy: 'cache-first',
      maxEntries: 100,
      maxAge: 30 * 24 * 60 * 60 // 30 days
    });
    
    // API responses - network first
    this.cacheConfigs.set('api', {
      name: 'mdedit-api',
      strategy: 'network-first',
      maxEntries: 50,
      maxAge: 5 * 60 // 5 minutes
    });
    
    // User content - stale while revalidate
    this.cacheConfigs.set('content', {
      name: 'mdedit-content',
      strategy: 'stale-while-revalidate',
      maxEntries: 100,
      maxAge: 24 * 60 * 60 // 24 hours
    });
  }
  
  async handleRequest(request: Request, cacheType: string): Promise<Response> {
    const config = this.cacheConfigs.get(cacheType);
    if (!config) {
      return fetch(request);
    }
    
    const cache = await caches.open(config.name);
    
    switch (config.strategy) {
      case 'cache-first':
        return this.cacheFirst(request, cache);
      case 'network-first':
        return this.networkFirst(request, cache);
      case 'stale-while-revalidate':
        return this.staleWhileRevalidate(request, cache);
      default:
        return fetch(request);
    }
  }
  
  private async cacheFirst(request: Request, cache: Cache): Promise<Response> {
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const response = await fetch(request);
    if (response.status === 200) {
      cache.put(request, response.clone());
    }
    
    return response;
  }
  
  private async networkFirst(request: Request, cache: Cache): Promise<Response> {
    try {
      const response = await fetch(request);
      if (response.status === 200) {
        cache.put(request, response.clone());
      }
      return response;
    } catch (error) {
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
      throw error;
    }
  }
  
  private async staleWhileRevalidate(request: Request, cache: Cache): Promise<Response> {
    const cachedResponse = await cache.match(request);
    
    // Fetch in background to update cache
    const fetchPromise = fetch(request).then(response => {
      if (response.status === 200) {
        cache.put(request, response.clone());
      }
      return response;
    });
    
    // Return cached response if available, otherwise wait for network
    return cachedResponse || fetchPromise;
  }
  
  async cleanupCaches(): Promise<void> {
    const cacheNames = await caches.keys();
    
    for (const cacheName of cacheNames) {
      const config = Array.from(this.cacheConfigs.values())
        .find(c => c.name === cacheName);
      
      if (config && (config.maxEntries || config.maxAge)) {
        await this.cleanupCache(cacheName, config);
      }
    }
  }
  
  private async cleanupCache(cacheName: string, config: CacheConfig): Promise<void> {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    // Sort by last accessed (if available) or creation time
    const requestsWithTime = await Promise.all(
      requests.map(async (request) => {
        const response = await cache.match(request);
        const dateHeader = response?.headers.get('date');
        const time = dateHeader ? new Date(dateHeader).getTime() : Date.now();
        return { request, time };
      })
    );
    
    requestsWithTime.sort((a, b) => b.time - a.time);
    
    // Remove excess entries
    if (config.maxEntries && requests.length > config.maxEntries) {
      const toDelete = requestsWithTime.slice(config.maxEntries);
      await Promise.all(toDelete.map(item => cache.delete(item.request)));
    }
    
    // Remove expired entries
    if (config.maxAge) {
      const now = Date.now();
      const expired = requestsWithTime.filter(
        item => now - item.time > config.maxAge! * 1000
      );
      await Promise.all(expired.map(item => cache.delete(item.request)));
    }
  }
}
```

## Testing
### Testing Standards
- **PWA Tests:** Test installation across all supported platforms
- **Caching Tests:** Verify caching strategies and offline functionality
- **Manifest Tests:** Validate manifest compliance and icon display
- **Service Worker Tests:** Test background sync and push notifications

### Key Test Scenarios
1. PWA installation on Chrome, Edge, Safari, and mobile browsers
2. Service worker caching and offline functionality
3. Manifest validation and icon display consistency
4. Standalone mode functionality without browser chrome
5. App shortcuts and deep linking
6. Background sync for offline operations
7. Cache cleanup and version management
8. Installation prompt timing and user experience

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*