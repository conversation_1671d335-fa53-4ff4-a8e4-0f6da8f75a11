# <!-- Powered by BMAD™ Core -->

# Story 3.3: Multi-Format Export Capabilities

## Status
Draft

## Story
**As a** user,
**I want** to export my markdown content to different formats,
**so that** I can use my content across various platforms and contexts.

## Acceptance Criteria
1. HTML export with clean, semantic markup and embedded CSS styling
2. PDF export with proper typography, page breaks, and print-friendly formatting
3. Export options preserve GitHub Flavored Markdown formatting including tables and code blocks
4. Custom CSS templates for HTML export to match different website styles
5. PDF export includes metadata (title, author) and table of contents for long documents
6. Export filename customization with automatic file extension handling
7. Batch export option for multiple formats simultaneously
8. Export preview functionality to review output before saving
9. Export settings persistence for consistent output formatting

## Tasks / Subtasks

- [ ] Create export system architecture (AC: 1, 2, 3)
  - [ ] Implement ExportManager service in src/services/
  - [ ] Create format-specific exporters (HTML, PDF)
  - [ ] Ensure GFM formatting preservation
  - [ ] Add export progress tracking

- [ ] Implement HTML export functionality (AC: 1, 4)
  - [ ] Create HTMLExporter with clean semantic markup
  - [ ] Add CSS embedding and template system
  - [ ] Support custom CSS templates
  - [ ] Generate responsive HTML output

- [ ] Add PDF export capabilities (AC: 2, 5)
  - [ ] Install and configure PDF generation library (Puppeteer/jsPDF)
  - [ ] Implement PDF formatting with typography controls
  - [ ] Add metadata support (title, author, creation date)
  - [ ] Generate table of contents for long documents

- [ ] Create export customization interface (AC: 6, 9)
  - [ ] Add export settings panel
  - [ ] Implement filename customization
  - [ ] Create export format selection UI
  - [ ] Add settings persistence

- [ ] Implement batch export functionality (AC: 7)
  - [ ] Create batch export interface
  - [ ] Support multiple simultaneous format exports
  - [ ] Add progress tracking for batch operations
  - [ ] Handle export errors gracefully

- [ ] Add export preview system (AC: 8)
  - [ ] Create export preview modal
  - [ ] Show formatted output before saving
  - [ ] Allow preview adjustments
  - [ ] Support preview for all export formats

- [ ] Create export templates and styling (AC: 4, 5)
  - [ ] Design HTML template system
  - [ ] Create PDF styling templates
  - [ ] Add popular preset templates
  - [ ] Support custom template creation

## Dev Notes

### Export System Architecture
```typescript
// Multi-Format Export System
interface ExportOptions {
  format: 'html' | 'pdf' | 'docx' | 'txt';
  filename?: string;
  template?: string;
  includeCSS?: boolean;
  includeTOC?: boolean;
  metadata?: {
    title?: string;
    author?: string;
    description?: string;
    keywords?: string[];
  };
  styling?: {
    theme: 'light' | 'dark' | 'print';
    fontSize: number;
    fontFamily: string;
    pageSize?: 'A4' | 'Letter' | 'Legal';
    margins?: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
  };
}

interface ExportResult {
  success: boolean;
  filename: string;
  format: string;
  size: number;
  error?: string;
  downloadUrl?: string;
}

class ExportManager {
  private exporters = new Map<string, Exporter>();
  
  constructor() {
    this.initializeExporters();
  }
  
  private initializeExporters() {
    this.exporters.set('html', new HTMLExporter());
    this.exporters.set('pdf', new PDFExporter());
    this.exporters.set('txt', new TextExporter());
  }
  
  async export(
    content: string, 
    options: ExportOptions,
    onProgress?: (progress: number) => void
  ): Promise<ExportResult> {
    const exporter = this.exporters.get(options.format);
    if (!exporter) {
      return {
        success: false,
        filename: '',
        format: options.format,
        size: 0,
        error: `Unsupported format: ${options.format}`
      };
    }
    
    try {
      onProgress?.(0.1);
      
      // Pre-process content
      const processedContent = await this.preprocessContent(content, options);
      onProgress?.(0.3);
      
      // Generate export
      const result = await exporter.export(processedContent, options);
      onProgress?.(0.8);
      
      // Post-process and create download
      const downloadUrl = await this.createDownloadUrl(result.data, options.format);
      onProgress?.(1.0);
      
      return {
        success: true,
        filename: options.filename || this.generateFilename(options.format),
        format: options.format,
        size: result.data.length,
        downloadUrl
      };
    } catch (error) {
      return {
        success: false,
        filename: '',
        format: options.format,
        size: 0,
        error: error.message
      };
    }
  }
  
  async batchExport(
    content: string,
    formats: ExportOptions[],
    onProgress?: (completed: number, total: number) => void
  ): Promise<ExportResult[]> {
    const results: ExportResult[] = [];
    
    for (let i = 0; i < formats.length; i++) {
      const result = await this.export(content, formats[i]);
      results.push(result);
      onProgress?.(i + 1, formats.length);
    }
    
    return results;
  }
  
  async preview(content: string, options: ExportOptions): Promise<string> {
    const exporter = this.exporters.get(options.format);
    if (!exporter) {
      throw new Error(`Unsupported format: ${options.format}`);
    }
    
    const processedContent = await this.preprocessContent(content, options);
    return exporter.preview(processedContent, options);
  }
}
```

### HTML Exporter Implementation
```typescript
// HTML Export with Template System
class HTMLExporter implements Exporter {
  private templates = new Map<string, HTMLTemplate>();
  
  constructor() {
    this.initializeTemplates();
  }
  
  private initializeTemplates() {
    // GitHub-style template
    this.templates.set('github', {
      id: 'github',
      name: 'GitHub Style',
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>{{title}}</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
              line-height: 1.5;
              color: #24292f;
              max-width: 980px;
              margin: 0 auto;
              padding: 45px;
            }
            {{customCSS}}
          </style>
        </head>
        <body>
          {{content}}
        </body>
        </html>
      `,
      css: `
        /* GitHub-style CSS */
        h1, h2 { border-bottom: 1px solid #d0d7de; padding-bottom: 0.3em; }
        pre { background-color: #f6f8fa; border-radius: 6px; padding: 16px; }
        code { background-color: rgba(175, 184, 193, 0.2); padding: 0.2em 0.4em; }
        table { border-collapse: collapse; }
        th, td { border: 1px solid #d0d7de; padding: 6px 13px; }
        blockquote { border-left: 4px solid #d0d7de; padding-left: 16px; margin: 0; }
      `
    });
    
    // Clean/minimal template
    this.templates.set('minimal', {
      id: 'minimal',
      name: 'Minimal',
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>{{title}}</title>
          <style>
            body {
              font-family: Georgia, 'Times New Roman', serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 2rem;
            }
            {{customCSS}}
          </style>
        </head>
        <body>
          {{content}}
        </body>
        </html>
      `,
      css: `
        h1, h2, h3, h4, h5, h6 { margin-top: 2em; margin-bottom: 0.5em; }
        pre { background-color: #f8f8f8; border: 1px solid #ddd; padding: 1em; }
        code { background-color: #f0f0f0; padding: 0.1em 0.3em; }
        blockquote { border-left: 3px solid #ccc; padding-left: 1em; margin-left: 0; }
      `
    });
  }
  
  async export(content: string, options: ExportOptions): Promise<ExportData> {
    const template = this.templates.get(options.template || 'github')!;
    const processedContent = await this.renderMarkdown(content);
    
    // Generate table of contents if requested
    let tocHtml = '';
    if (options.includeTOC) {
      tocHtml = this.generateTableOfContents(processedContent);
    }
    
    // Replace template variables
    let html = template.html
      .replace('{{title}}', options.metadata?.title || 'Markdown Document')
      .replace('{{content}}', tocHtml + processedContent)
      .replace('{{customCSS}}', template.css + (options.styling?.customCSS || ''));
    
    // Add metadata
    if (options.metadata) {
      html = this.addMetadata(html, options.metadata);
    }
    
    return {
      data: html,
      mimeType: 'text/html'
    };
  }
  
  async preview(content: string, options: ExportOptions): Promise<string> {
    const result = await this.export(content, options);
    return result.data;
  }
  
  private generateTableOfContents(html: string): string {
    const headings = html.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi) || [];
    if (headings.length === 0) return '';
    
    let toc = '<div class="table-of-contents"><h2>Table of Contents</h2><ul>';
    
    headings.forEach((heading, index) => {
      const level = parseInt(heading.match(/<h([1-6])/i)![1]);
      const text = heading.replace(/<[^>]*>/g, '');
      const id = `heading-${index}`;
      
      toc += `<li class="toc-level-${level}"><a href="#${id}">${text}</a></li>`;
      
      // Add ID to original heading
      html = html.replace(heading, heading.replace('<h' + level, `<h${level} id="${id}"`));
    });
    
    toc += '</ul></div>';
    return toc;
  }
}
```

### PDF Exporter Implementation
```typescript
// PDF Export with Puppeteer
class PDFExporter implements Exporter {
  private puppeteer: any;
  
  async export(content: string, options: ExportOptions): Promise<ExportData> {
    // First generate HTML
    const htmlExporter = new HTMLExporter();
    const htmlResult = await htmlExporter.export(content, {
      ...options,
      template: 'print' // Use print-friendly template
    });
    
    // Generate PDF using Puppeteer
    const browser = await this.puppeteer.launch({ headless: true });
    const page = await browser.newPage();
    
    await page.setContent(htmlResult.data);
    
    const pdfBuffer = await page.pdf({
      format: options.styling?.pageSize || 'A4',
      margin: {
        top: options.styling?.margins?.top || 20,
        right: options.styling?.margins?.right || 20,
        bottom: options.styling?.margins?.bottom || 20,
        left: options.styling?.margins?.left || 20
      },
      printBackground: true,
      displayHeaderFooter: true,
      headerTemplate: this.generateHeader(options.metadata),
      footerTemplate: this.generateFooter(),
    });
    
    await browser.close();
    
    return {
      data: pdfBuffer,
      mimeType: 'application/pdf'
    };
  }
  
  private generateHeader(metadata?: ExportOptions['metadata']): string {
    if (!metadata?.title) return '';
    
    return `
      <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
        ${metadata.title}
      </div>
    `;
  }
  
  private generateFooter(): string {
    return `
      <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
        Page <span class="pageNumber"></span> of <span class="totalPages"></span>
      </div>
    `;
  }
}
```

### Export UI Components
```typescript
// Export Interface Components
const ExportDialog: React.FC<{ content: string; isOpen: boolean; onClose: () => void }> = ({
  content,
  isOpen,
  onClose
}) => {
  const [options, setOptions] = useState<ExportOptions>({
    format: 'html',
    filename: 'document',
    template: 'github',
    includeCSS: true,
    includeTOC: false,
    metadata: {
      title: 'Markdown Document',
      author: '',
      description: ''
    }
  });
  
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [previewMode, setPreviewMode] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  
  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);
    
    try {
      const exportManager = new ExportManager();
      const result = await exportManager.export(
        content, 
        options,
        (progress) => setExportProgress(progress * 100)
      );
      
      if (result.success && result.downloadUrl) {
        // Trigger download
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = result.filename;
        link.click();
        
        onClose();
      } else {
        alert(`Export failed: ${result.error}`);
      }
    } catch (error) {
      alert(`Export failed: ${error.message}`);
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };
  
  const handlePreview = async () => {
    try {
      const exportManager = new ExportManager();
      const preview = await exportManager.preview(content, options);
      setPreviewContent(preview);
      setPreviewMode(true);
    } catch (error) {
      alert(`Preview failed: ${error.message}`);
    }
  };
  
  const handleBatchExport = async () => {
    const formats: ExportOptions[] = [
      { ...options, format: 'html' },
      { ...options, format: 'pdf' },
      { ...options, format: 'txt' }
    ];
    
    setIsExporting(true);
    
    try {
      const exportManager = new ExportManager();
      const results = await exportManager.batchExport(
        content,
        formats,
        (completed, total) => setExportProgress((completed / total) * 100)
      );
      
      // Process results and trigger downloads
      results.forEach(result => {
        if (result.success && result.downloadUrl) {
          const link = document.createElement('a');
          link.href = result.downloadUrl;
          link.download = result.filename;
          link.click();
        }
      });
      
      onClose();
    } catch (error) {
      alert(`Batch export failed: ${error.message}`);
    } finally {
      setIsExporting(false);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="export-dialog-overlay">
      <div className="export-dialog">
        <div className="export-dialog-header">
          <h2>Export Document</h2>
          <button onClick={onClose}>×</button>
        </div>
        
        <div className="export-dialog-content">
          <div className="export-options">
            <div className="option-group">
              <label>Format:</label>
              <select 
                value={options.format} 
                onChange={(e) => setOptions({...options, format: e.target.value as any})}
              >
                <option value="html">HTML</option>
                <option value="pdf">PDF</option>
                <option value="txt">Plain Text</option>
              </select>
            </div>
            
            <div className="option-group">
              <label>Filename:</label>
              <input 
                type="text" 
                value={options.filename} 
                onChange={(e) => setOptions({...options, filename: e.target.value})}
              />
            </div>
            
            <div className="option-group">
              <label>Template:</label>
              <select 
                value={options.template} 
                onChange={(e) => setOptions({...options, template: e.target.value})}
              >
                <option value="github">GitHub Style</option>
                <option value="minimal">Minimal</option>
                <option value="academic">Academic</option>
              </select>
            </div>
            
            <div className="option-group">
              <label>
                <input 
                  type="checkbox" 
                  checked={options.includeTOC} 
                  onChange={(e) => setOptions({...options, includeTOC: e.target.checked})}
                />
                Include Table of Contents
              </label>
            </div>
          </div>
          
          {isExporting && (
            <div className="export-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${exportProgress}%` }}
                />
              </div>
              <span>{Math.round(exportProgress)}%</span>
            </div>
          )}
        </div>
        
        <div className="export-dialog-actions">
          <button onClick={handlePreview} disabled={isExporting}>
            Preview
          </button>
          <button onClick={handleBatchExport} disabled={isExporting}>
            Batch Export
          </button>
          <button onClick={handleExport} disabled={isExporting}>
            {isExporting ? 'Exporting...' : 'Export'}
          </button>
        </div>
      </div>
      
      {previewMode && (
        <ExportPreviewModal 
          content={previewContent}
          format={options.format}
          onClose={() => setPreviewMode(false)}
        />
      )}
    </div>
  );
};
```

## Testing
### Testing Standards
- **Unit Tests:** Test export functionality for each format
- **Integration Tests:** Test batch export and template system
- **File Tests:** Verify exported file integrity and compatibility
- **Performance Tests:** Test export speed with large documents

### Key Test Scenarios
1. HTML export with embedded CSS and proper formatting
2. PDF export with metadata and table of contents
3. Custom template application and styling
4. Batch export of multiple formats simultaneously
5. Export preview accuracy for all formats
6. Filename customization and extension handling
7. Export settings persistence across sessions
8. Error handling for failed exports

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*