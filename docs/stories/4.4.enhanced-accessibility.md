# <!-- Powered by BMAD™ Core -->

# Story 4.4: Enhanced Accessibility (WCAG AA Compliance)

## Status
Draft

## Story
**As a** user with accessibility needs,
**I want** full keyboard navigation and screen reader support,
**so that** I can use MDEdit effectively regardless of my abilities.

## Acceptance Criteria
1. Complete keyboard navigation for all interface elements without mouse dependency
2. Screen reader compatibility with proper ARIA labels and semantic HTML structure
3. High contrast mode support with customizable color schemes for visual accessibility
4. Focus indicators clearly visible and properly managed throughout the interface
5. Skip links and landmark navigation for efficient screen reader usage
6. Alternative text and descriptions for all visual elements and state changes
7. Keyboard shortcuts documented and customizable for users with motor disabilities
8. Text scaling support up to 200% without loss of functionality or layout breaking
9. WCAG AA compliance verified through automated testing and manual accessibility audits

## Tasks / Subtasks

- [ ] Implement comprehensive keyboard navigation (AC: 1, 7)
  - [ ] Create KeyboardNavigationManager for consistent navigation
  - [ ] Ensure all interactive elements are keyboard accessible
  - [ ] Implement logical tab order throughout the application
  - [ ] Add keyboard shortcut documentation and customization

- [ ] Add screen reader support (AC: 2, 6)
  - [ ] Implement proper semantic HTML structure
  - [ ] Add comprehensive ARIA labels and descriptions
  - [ ] Create screen reader announcements for dynamic content
  - [ ] Test with popular screen readers (NVDA, JAWS, VoiceOver)

- [ ] Create high contrast and visual accessibility modes (AC: 3)
  - [ ] Implement WCAG AA compliant color schemes
  - [ ] Add high contrast mode toggle
  - [ ] Create customizable color options for different visual needs
  - [ ] Ensure sufficient color contrast ratios (4.5:1 minimum)

- [ ] Implement focus management system (AC: 4)
  - [ ] Create visible focus indicators for all interactive elements
  - [ ] Implement focus trapping for modal dialogs
  - [ ] Manage focus during dynamic content changes
  - [ ] Add focus restoration after modal closure

- [ ] Add landmark navigation and skip links (AC: 5)
  - [ ] Implement ARIA landmarks for major page sections
  - [ ] Add skip links for main content areas
  - [ ] Create navigation shortcuts for screen readers
  - [ ] Implement heading structure hierarchy

- [ ] Ensure text scaling and responsive accessibility (AC: 8)
  - [ ] Test layout at 200% zoom level
  - [ ] Implement responsive text and UI scaling
  - [ ] Ensure functionality maintains at all zoom levels
  - [ ] Add user preference for text size

- [ ] Create accessibility testing and validation (AC: 9)
  - [ ] Implement automated accessibility testing
  - [ ] Conduct manual testing with screen readers
  - [ ] Validate WCAG AA compliance with axe-core
  - [ ] Create accessibility audit reports

- [ ] Document accessibility features (AC: 7)
  - [ ] Create comprehensive accessibility documentation
  - [ ] Document all keyboard shortcuts and navigation
  - [ ] Provide user guides for assistive technology
  - [ ] Create accessibility statement

## Dev Notes

### Keyboard Navigation Management
```typescript
// Comprehensive Keyboard Navigation System
interface KeyboardNavigation {
  element: HTMLElement;
  role: 'button' | 'link' | 'input' | 'menu' | 'dialog' | 'listbox';
  tabIndex: number;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  shortcuts?: KeyboardShortcut[];
}

interface KeyboardShortcut {
  key: string;
  description: string;
  handler: () => void;
  global?: boolean;
}

class KeyboardNavigationManager {
  private navigableElements: Map<string, KeyboardNavigation> = new Map();
  private currentFocus: HTMLElement | null = null;
  private focusHistory: HTMLElement[] = [];
  private trapFocus = false;
  private trapContainer: HTMLElement | null = null;
  
  constructor() {
    this.setupEventListeners();
    this.registerDefaultShortcuts();
  }
  
  private setupEventListeners(): void {
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));
    
    // Handle Escape key globally
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.handleEscape();
      }
    });
  }
  
  private registerDefaultShortcuts(): void {
    // Global navigation shortcuts
    this.addGlobalShortcut('Alt+1', 'Skip to main content', () => {
      this.skipToContent('main');
    });
    
    this.addGlobalShortcut('Alt+2', 'Skip to navigation', () => {
      this.skipToContent('navigation');
    });
    
    this.addGlobalShortcut('Alt+3', 'Skip to editor', () => {
      this.skipToContent('editor');
    });
    
    // Focus management shortcuts
    this.addGlobalShortcut('F6', 'Cycle through major sections', () => {
      this.cycleThroughLandmarks();
    });
    
    this.addGlobalShortcut('Ctrl+F6', 'Navigate between panes', () => {
      this.navigateBetweenPanes();
    });
  }
  
  registerNavigableElement(
    id: string, 
    element: HTMLElement, 
    config: Omit<KeyboardNavigation, 'element'>
  ): void {
    // Set up proper attributes
    element.tabIndex = config.tabIndex;
    
    if (config.ariaLabel) {
      element.setAttribute('aria-label', config.ariaLabel);
    }
    
    if (config.ariaDescribedBy) {
      element.setAttribute('aria-describedby', config.ariaDescribedBy);
    }
    
    // Set role if not inherent
    if (config.role && !this.hasInherentRole(element, config.role)) {
      element.setAttribute('role', config.role);
    }
    
    this.navigableElements.set(id, {
      ...config,
      element
    });
  }
  
  private hasInherentRole(element: HTMLElement, role: string): boolean {
    const tagName = element.tagName.toLowerCase();
    
    const inherentRoles: Record<string, string[]> = {
      'button': ['button'],
      'link': ['a'],
      'input': ['input', 'textarea', 'select'],
      'menu': ['nav', 'ul', 'ol']
    };
    
    return inherentRoles[role]?.includes(tagName) || false;
  }
  
  private handleKeyDown(e: KeyboardEvent): void {
    const target = e.target as HTMLElement;
    
    // Handle Tab navigation
    if (e.key === 'Tab') {
      this.handleTabNavigation(e);
    }
    
    // Handle Arrow key navigation for specific contexts
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
      if (this.isInMenuContext(target)) {
        e.preventDefault();
        this.handleArrowNavigation(e, target);
      }
    }
    
    // Handle Enter and Space for activation
    if (e.key === 'Enter' || e.key === ' ') {
      if (this.isActivatable(target)) {
        e.preventDefault();
        this.activateElement(target);
      }
    }
    
    // Handle global shortcuts
    const shortcutKey = this.getShortcutKey(e);
    this.handleGlobalShortcuts(shortcutKey, e);
  }
  
  private handleTabNavigation(e: KeyboardEvent): void {
    if (this.trapFocus && this.trapContainer) {
      const focusableElements = this.getFocusableElements(this.trapContainer);
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];
      
      if (e.shiftKey) {
        // Shift+Tab
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    }
  }
  
  private handleArrowNavigation(e: KeyboardEvent, target: HTMLElement): void {
    const parent = target.closest('[role="menu"], [role="listbox"], [role="tablist"]');
    if (!parent) return;
    
    const siblings = Array.from(parent.querySelectorAll('[tabindex="0"], [tabindex="-1"]'));
    const currentIndex = siblings.indexOf(target);
    
    let nextIndex = currentIndex;
    
    if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
      nextIndex = (currentIndex + 1) % siblings.length;
    } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
      nextIndex = currentIndex === 0 ? siblings.length - 1 : currentIndex - 1;
    }
    
    if (nextIndex !== currentIndex) {
      (siblings[nextIndex] as HTMLElement).focus();
    }
  }
  
  enableFocusTrap(container: HTMLElement): void {
    this.trapFocus = true;
    this.trapContainer = container;
    
    // Focus first focusable element
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }
  
  disableFocusTrap(): void {
    this.trapFocus = false;
    this.trapContainer = null;
    
    // Restore focus to previous element
    if (this.focusHistory.length > 0) {
      const previousFocus = this.focusHistory.pop();
      if (previousFocus && document.body.contains(previousFocus)) {
        previousFocus.focus();
      }
    }
  }
  
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const selectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable]'
    ].join(', ');
    
    return Array.from(container.querySelectorAll(selectors));
  }
  
  skipToContent(landmarkType: string): void {
    const landmarks: Record<string, string[]> = {
      'main': ['main', '[role="main"]'],
      'navigation': ['nav', '[role="navigation"]'],
      'editor': ['.editor', '[role="textbox"]'],
      'preview': ['.preview', '[role="document"]']
    };
    
    const selectors = landmarks[landmarkType];
    if (selectors) {
      for (const selector of selectors) {
        const element = document.querySelector(selector) as HTMLElement;
        if (element) {
          element.focus();
          element.scrollIntoView({ behavior: 'smooth' });
          
          // Announce the skip
          this.announceToScreenReader(`Skipped to ${landmarkType}`);
          break;
        }
      }
    }
  }
  
  announceToScreenReader(message: string): void {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
}
```

### Screen Reader Support Implementation
```typescript
// Comprehensive Screen Reader Support
class ScreenReaderSupport {
  private liveRegions: Map<string, HTMLElement> = new Map();
  
  constructor() {
    this.setupLiveRegions();
    this.setupDocumentStructure();
  }
  
  private setupLiveRegions(): void {
    // Status announcements (polite)
    this.createLiveRegion('status', 'polite');
    
    // Error announcements (assertive)
    this.createLiveRegion('errors', 'assertive');
    
    // Navigation announcements (polite)
    this.createLiveRegion('navigation', 'polite');
  }
  
  private createLiveRegion(id: string, politeness: 'polite' | 'assertive'): void {
    const region = document.createElement('div');
    region.id = `sr-live-${id}`;
    region.setAttribute('aria-live', politeness);
    region.setAttribute('aria-atomic', 'true');
    region.className = 'sr-only';
    
    document.body.appendChild(region);
    this.liveRegions.set(id, region);
  }
  
  private setupDocumentStructure(): void {
    // Ensure proper heading hierarchy
    this.validateHeadingStructure();
    
    // Add landmark roles where missing
    this.addMissingLandmarks();
    
    // Enhance form elements
    this.enhanceFormElements();
  }
  
  private validateHeadingStructure(): void {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      // Check for proper hierarchy
      if (index === 0 && level !== 1) {
        console.warn('Document should start with h1');
      }
      
      if (level > previousLevel + 1) {
        console.warn(`Heading level skip: h${previousLevel} to h${level}`);
      }
      
      previousLevel = level;
    });
  }
  
  private addMissingLandmarks(): void {
    // Add main landmark if missing
    if (!document.querySelector('main, [role="main"]')) {
      const mainContent = document.querySelector('.main-content, .app-content');
      if (mainContent) {
        mainContent.setAttribute('role', 'main');
        mainContent.setAttribute('aria-label', 'Main content');
      }
    }
    
    // Add navigation landmarks
    const navElements = document.querySelectorAll('nav');
    navElements.forEach((nav, index) => {
      if (!nav.hasAttribute('aria-label')) {
        nav.setAttribute('aria-label', `Navigation ${index + 1}`);
      }
    });
    
    // Add complementary landmarks
    const sidebars = document.querySelectorAll('.sidebar, .secondary');
    sidebars.forEach((sidebar) => {
      if (!sidebar.hasAttribute('role')) {
        sidebar.setAttribute('role', 'complementary');
      }
    });
  }
  
  private enhanceFormElements(): void {
    // Enhance input elements
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach((input) => {
      const element = input as HTMLElement;
      
      // Ensure proper labeling
      if (!element.hasAttribute('aria-label') && !element.hasAttribute('aria-labelledby')) {
        const label = document.querySelector(`label[for="${element.id}"]`);
        if (!label && element.id) {
          console.warn(`Input ${element.id} missing label`);
        }
      }
      
      // Add descriptions for validation
      const errorElement = document.querySelector(`[data-error-for="${element.id}"]`);
      if (errorElement) {
        element.setAttribute('aria-describedby', errorElement.id);
        element.setAttribute('aria-invalid', 'true');
      }
    });
    
    // Enhance buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach((button) => {
      if (!button.textContent?.trim() && !button.hasAttribute('aria-label')) {
        console.warn('Button missing accessible text');
      }
    });
  }
  
  announce(message: string, type: 'status' | 'error' | 'navigation' = 'status'): void {
    const region = this.liveRegions.get(type === 'error' ? 'errors' : type);
    if (region) {
      region.textContent = message;
      
      // Clear after announcement to allow repeated messages
      setTimeout(() => {
        region.textContent = '';
      }, 100);
    }
  }
  
  announcePageChange(newPageTitle: string): void {
    this.announce(`Navigated to ${newPageTitle}`, 'navigation');
  }
  
  announceFileOperation(operation: string, filename: string): void {
    this.announce(`${operation} ${filename}`, 'status');
  }
  
  announceError(error: string): void {
    this.announce(`Error: ${error}`, 'error');
  }
  
  describeEditor(editorState: any): string {
    const { lineCount, selection, mode } = editorState;
    
    let description = `Text editor with ${lineCount} lines. `;
    
    if (selection) {
      if (selection.start === selection.end) {
        description += `Cursor at line ${selection.line}, column ${selection.column}. `;
      } else {
        description += `${selection.length} characters selected. `;
      }
    }
    
    if (mode === 'vim') {
      description += 'Vim mode enabled. ';
    }
    
    return description;
  }
  
  describePreview(previewState: any): string {
    const { wordCount, headingCount } = previewState;
    
    return `Preview pane showing rendered markdown with ${wordCount} words and ${headingCount} headings.`;
  }
}
```

### High Contrast and Visual Accessibility
```typescript
// WCAG AA Compliant Color System
interface ColorContrastRatio {
  background: string;
  foreground: string;
  ratio: number;
  level: 'AA' | 'AAA' | 'FAIL';
}

interface AccessibilityTheme {
  id: string;
  name: string;
  description: string;
  colors: {
    // Background colors
    background: string;
    surface: string;
    overlay: string;
    
    // Text colors
    primary: string;
    secondary: string;
    disabled: string;
    
    // Interactive colors
    link: string;
    linkHover: string;
    linkVisited: string;
    
    // State colors
    focus: string;
    error: string;
    warning: string;
    success: string;
    
    // Editor colors
    editorBackground: string;
    editorText: string;
    editorSelection: string;
    editorCursor: string;
    
    // Syntax highlighting (high contrast)
    syntaxKeyword: string;
    syntaxString: string;
    syntaxComment: string;
    syntaxNumber: string;
  };
  contrastRatios: Record<string, ColorContrastRatio>;
}

class AccessibilityThemeManager {
  private themes: Map<string, AccessibilityTheme> = new Map();
  private currentTheme: AccessibilityTheme;
  
  constructor() {
    this.initializeThemes();
    this.currentTheme = this.themes.get('high-contrast-light')!;
  }
  
  private initializeThemes(): void {
    // High Contrast Light Theme
    this.themes.set('high-contrast-light', {
      id: 'high-contrast-light',
      name: 'High Contrast Light',
      description: 'High contrast light theme for visual accessibility',
      colors: {
        background: '#ffffff',
        surface: '#f8f9fa',
        overlay: '#ffffff',
        primary: '#000000',
        secondary: '#333333',
        disabled: '#666666',
        link: '#0000ff',
        linkHover: '#0000cc',
        linkVisited: '#551a8b',
        focus: '#ff8c00',
        error: '#d00000',
        warning: '#ff8c00',
        success: '#008000',
        editorBackground: '#ffffff',
        editorText: '#000000',
        editorSelection: '#0078d4',
        editorCursor: '#000000',
        syntaxKeyword: '#0000ff',
        syntaxString: '#008000',
        syntaxComment: '#666666',
        syntaxNumber: '#d00000'
      },
      contrastRatios: {}
    });
    
    // High Contrast Dark Theme
    this.themes.set('high-contrast-dark', {
      id: 'high-contrast-dark',
      name: 'High Contrast Dark',
      description: 'High contrast dark theme for visual accessibility',
      colors: {
        background: '#000000',
        surface: '#1a1a1a',
        overlay: '#000000',
        primary: '#ffffff',
        secondary: '#cccccc',
        disabled: '#666666',
        link: '#00ffff',
        linkHover: '#66ffff',
        linkVisited: '#ff66ff',
        focus: '#ffff00',
        error: '#ff6666',
        warning: '#ffff66',
        success: '#66ff66',
        editorBackground: '#000000',
        editorText: '#ffffff',
        editorSelection: '#0078d4',
        editorCursor: '#ffffff',
        syntaxKeyword: '#66ccff',
        syntaxString: '#66ff66',
        syntaxComment: '#999999',
        syntaxNumber: '#ff6666'
      },
      contrastRatios: {}
    });
    
    // Calculate contrast ratios for each theme
    this.themes.forEach(theme => {
      this.calculateContrastRatios(theme);
    });
  }
  
  private calculateContrastRatios(theme: AccessibilityTheme): void {
    const combinations = [
      ['primary', 'background'],
      ['secondary', 'background'],
      ['link', 'background'],
      ['error', 'background'],
      ['warning', 'background'],
      ['success', 'background'],
      ['editorText', 'editorBackground']
    ];
    
    combinations.forEach(([foreground, background]) => {
      const fgColor = theme.colors[foreground as keyof typeof theme.colors];
      const bgColor = theme.colors[background as keyof typeof theme.colors];
      
      const ratio = this.calculateContrastRatio(fgColor, bgColor);
      const level = this.getWCAGLevel(ratio);
      
      theme.contrastRatios[`${foreground}-${background}`] = {
        foreground: fgColor,
        background: bgColor,
        ratio,
        level
      };
    });
  }
  
  private calculateContrastRatio(foreground: string, background: string): number {
    const fgLuminance = this.getLuminance(foreground);
    const bgLuminance = this.getLuminance(background);
    
    const lighter = Math.max(fgLuminance, bgLuminance);
    const darker = Math.min(fgLuminance, bgLuminance);
    
    return (lighter + 0.05) / (darker + 0.05);
  }
  
  private getLuminance(color: string): number {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Convert to relative luminance
    const [rs, gs, bs] = [r, g, b].map(c => {
      const sRGB = c / 255;
      return sRGB <= 0.03928 ? sRGB / 12.92 : Math.pow((sRGB + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }
  
  private getWCAGLevel(ratio: number): 'AA' | 'AAA' | 'FAIL' {
    if (ratio >= 7) return 'AAA';
    if (ratio >= 4.5) return 'AA';
    return 'FAIL';
  }
  
  applyTheme(themeId: string): void {
    const theme = this.themes.get(themeId);
    if (!theme) return;
    
    this.currentTheme = theme;
    
    // Apply CSS custom properties
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
    
    // Update body class
    document.body.className = document.body.className
      .replace(/accessibility-theme-\w+/g, '')
      .concat(` accessibility-theme-${themeId}`);
    
    // Announce theme change
    const screenReader = new ScreenReaderSupport();
    screenReader.announce(`Switched to ${theme.name} theme`);
  }
  
  validateCurrentTheme(): { passed: number; failed: number; total: number } {
    let passed = 0;
    let failed = 0;
    let total = 0;
    
    Object.values(this.currentTheme.contrastRatios).forEach(ratio => {
      total++;
      if (ratio.level === 'FAIL') {
        failed++;
      } else {
        passed++;
      }
    });
    
    return { passed, failed, total };
  }
  
  getAccessibilityReport(): string {
    const validation = this.validateCurrentTheme();
    const theme = this.currentTheme;
    
    let report = `Accessibility Report for ${theme.name}\n`;
    report += `=====================================\n\n`;
    report += `Color Contrast Validation:\n`;
    report += `- Passed: ${validation.passed}/${validation.total}\n`;
    report += `- Failed: ${validation.failed}/${validation.total}\n\n`;
    
    if (validation.failed > 0) {
      report += `Failed Combinations:\n`;
      Object.entries(theme.contrastRatios).forEach(([key, ratio]) => {
        if (ratio.level === 'FAIL') {
          report += `- ${key}: ${ratio.ratio.toFixed(2)}:1 (${ratio.foreground} on ${ratio.background})\n`;
        }
      });
    }
    
    return report;
  }
}
```

### Focus Management System
```typescript
// Advanced Focus Management
class FocusManager {
  private focusHistory: HTMLElement[] = [];
  private modalStack: HTMLElement[] = [];
  private currentTrap: HTMLElement | null = null;
  
  constructor() {
    this.setupFocusIndicators();
    this.setupEventListeners();
  }
  
  private setupFocusIndicators(): void {
    // Enhanced focus styles
    const style = document.createElement('style');
    style.textContent = `
      /* High visibility focus indicators */
      *:focus {
        outline: 3px solid var(--color-focus);
        outline-offset: 2px;
        border-radius: 2px;
      }
      
      /* Skip links */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--color-focus);
        color: var(--color-background);
        padding: 8px;
        text-decoration: none;
        border-radius: 0 0 4px 4px;
        z-index: 9999;
      }
      
      .skip-link:focus {
        top: 0;
      }
      
      /* Focus within indicators */
      .focus-within {
        box-shadow: 0 0 0 2px var(--color-focus);
      }
      
      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        *:focus {
          transition: none;
        }
      }
    `;
    document.head.appendChild(style);
    
    // Add skip links
    this.addSkipLinks();
  }
  
  private addSkipLinks(): void {
    const skipLinks = document.createElement('div');
    skipLinks.className = 'skip-links';
    skipLinks.innerHTML = `
      <a href="#main-content" class="skip-link">Skip to main content</a>
      <a href="#editor" class="skip-link">Skip to editor</a>
      <a href="#preview" class="skip-link">Skip to preview</a>
      <a href="#navigation" class="skip-link">Skip to navigation</a>
    `;
    
    document.body.insertBefore(skipLinks, document.body.firstChild);
  }
  
  private setupEventListeners(): void {
    // Track focus changes
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));
    
    // Handle modal focus trapping
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab' && this.currentTrap) {
        this.handleTrapTabbing(e);
      }
    });
  }
  
  private handleFocusIn(e: FocusEvent): void {
    const target = e.target as HTMLElement;
    
    // Update focus history
    this.updateFocusHistory(target);
    
    // Add focus-within classes to parents
    this.addFocusWithinClasses(target);
    
    // Announce focus changes to screen reader if significant
    this.announceFocusChange(target);
  }
  
  private handleFocusOut(e: FocusEvent): void {
    const target = e.target as HTMLElement;
    
    // Remove focus-within classes
    this.removeFocusWithinClasses(target);
  }
  
  private updateFocusHistory(element: HTMLElement): void {
    // Don't track certain elements
    if (element.matches('.skip-link, [aria-live], .sr-only')) {
      return;
    }
    
    // Remove element if already in history
    const existingIndex = this.focusHistory.indexOf(element);
    if (existingIndex > -1) {
      this.focusHistory.splice(existingIndex, 1);
    }
    
    // Add to end of history
    this.focusHistory.push(element);
    
    // Limit history size
    if (this.focusHistory.length > 10) {
      this.focusHistory.shift();
    }
  }
  
  private addFocusWithinClasses(element: HTMLElement): void {
    let parent = element.parentElement;
    while (parent) {
      if (parent.matches('.editor, .preview, .toolbar, .modal')) {
        parent.classList.add('focus-within');
      }
      parent = parent.parentElement;
    }
  }
  
  private removeFocusWithinClasses(element: HTMLElement): void {
    // Remove focus-within from all elements
    document.querySelectorAll('.focus-within').forEach(el => {
      if (!el.querySelector(':focus')) {
        el.classList.remove('focus-within');
      }
    });
  }
  
  trapFocus(container: HTMLElement): void {
    this.currentTrap = container;
    
    // Save current focus
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus && !container.contains(currentFocus)) {
      this.focusHistory.push(currentFocus);
    }
    
    // Focus first focusable element
    const firstFocusable = this.getFirstFocusableElement(container);
    if (firstFocusable) {
      firstFocusable.focus();
    }
    
    // Add to modal stack
    this.modalStack.push(container);
  }
  
  releaseFocus(): void {
    if (this.modalStack.length === 0) return;
    
    // Remove from modal stack
    const releasedModal = this.modalStack.pop();
    
    // Update current trap
    this.currentTrap = this.modalStack.length > 0 ? 
      this.modalStack[this.modalStack.length - 1] : null;
    
    // Restore focus
    this.restoreFocus();
  }
  
  private restoreFocus(): void {
    // Try to restore to previous focus
    while (this.focusHistory.length > 0) {
      const previousFocus = this.focusHistory.pop();
      if (previousFocus && document.body.contains(previousFocus) && 
          this.isVisible(previousFocus)) {
        previousFocus.focus();
        return;
      }
    }
    
    // Fallback to body or main content
    const main = document.querySelector('main, [role="main"]') as HTMLElement;
    if (main) {
      main.focus();
    } else {
      document.body.focus();
    }
  }
  
  private handleTrapTabbing(e: KeyboardEvent): void {
    if (!this.currentTrap) return;
    
    const focusableElements = this.getFocusableElements(this.currentTrap);
    if (focusableElements.length === 0) return;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }
  
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"]):not([disabled])',
      '[contenteditable]:not([contenteditable="false"])'
    ].join(', ');
    
    return Array.from(container.querySelectorAll(selector))
      .filter(element => this.isVisible(element as HTMLElement)) as HTMLElement[];
  }
  
  private getFirstFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    return focusableElements.length > 0 ? focusableElements[0] : null;
  }
  
  private isVisible(element: HTMLElement): boolean {
    const style = getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           element.offsetParent !== null;
  }
  
  private announceFocusChange(element: HTMLElement): void {
    // Announce significant focus changes
    const significantElements = [
      'button[aria-label]',
      '[role="tab"]',
      '[role="menuitem"]',
      '.editor',
      '.preview'
    ];
    
    for (const selector of significantElements) {
      if (element.matches(selector)) {
        const label = element.getAttribute('aria-label') || 
                     element.textContent?.trim() ||
                     element.tagName.toLowerCase();
        
        const screenReader = new ScreenReaderSupport();
        screenReader.announce(`Focused ${label}`);
        break;
      }
    }
  }
}
```

## Testing
### Testing Standards
- **Accessibility Tests:** Automated testing with axe-core and manual testing with screen readers
- **Keyboard Tests:** Complete keyboard navigation testing without mouse
- **Contrast Tests:** WCAG AA color contrast validation
- **Screen Reader Tests:** Testing with NVDA, JAWS, and VoiceOver

### Key Test Scenarios
1. Complete keyboard navigation of all interface elements
2. Screen reader compatibility and proper announcements
3. High contrast mode functionality and color contrast ratios
4. Focus management and trap behavior in modals
5. Skip links and landmark navigation
6. Text scaling up to 200% without functionality loss
7. ARIA labels and descriptions accuracy
8. WCAG AA compliance validation

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*