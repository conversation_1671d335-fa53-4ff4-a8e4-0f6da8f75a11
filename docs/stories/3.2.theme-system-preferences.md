# <!-- Powered by BMAD™ Core -->

# Story 3.2: Theme System and User Preferences

## Status
Draft

## Story
**As a** user,
**I want** to customize the interface appearance and behavior to match my preferences,
**so that** I can work comfortably in my preferred environment.

## Acceptance Criteria
1. Light and dark theme options with automatic system preference detection
2. Custom color schemes for editor syntax highlighting with preset options
3. Typography preferences: font family, size, and line height customization
4. Layout preferences: editor/preview ratio, show/hide line numbers, word wrap settings
5. Preview styling options to match different output targets (GitHub, plain HTML, etc.)
6. Preference persistence using browser local storage across sessions
7. Import/export preference settings for sharing configurations
8. Preview theme switching between light/dark independent of editor theme
9. High contrast mode for accessibility compliance

## Tasks / Subtasks

- [ ] Create theme system architecture (AC: 1, 8)
  - [ ] Implement ThemeManager service in src/services/
  - [ ] Create light and dark theme definitions
  - [ ] Add system preference detection
  - [ ] Support independent editor and preview themes

- [ ] Design custom color scheme system (AC: 2)
  - [ ] Create ColorScheme interface and presets
  - [ ] Implement syntax highlighting theme editor
  - [ ] Add popular preset themes (GitHub, VS Code, Monokai, etc.)
  - [ ] Support custom color scheme creation

- [ ] Implement typography preferences (AC: 3)
  - [ ] Create font selection interface
  - [ ] Support common programming fonts
  - [ ] Add font size and line height controls
  - [ ] Implement real-time typography preview

- [ ] Add layout and editor preferences (AC: 4)
  - [ ] Create editor/preview ratio controls
  - [ ] Add line numbers and word wrap toggles
  - [ ] Implement layout persistence
  - [ ] Support custom layout configurations

- [ ] Create preview styling system (AC: 5)
  - [ ] Implement multiple preview style presets
  - [ ] Add GitHub-style, plain HTML, and custom CSS options
  - [ ] Create preview style editor
  - [ ] Support external CSS injection

- [ ] Implement preference persistence (AC: 6, 7)
  - [ ] Create PreferenceStorage service
  - [ ] Add localStorage integration
  - [ ] Implement import/export functionality
  - [ ] Add preference backup and restore

- [ ] Add accessibility features (AC: 9)
  - [ ] Implement high contrast mode
  - [ ] Add WCAG AA compliant color schemes
  - [ ] Create accessibility preference panel
  - [ ] Test with screen readers and assistive technology

## Dev Notes

### Theme System Architecture
```typescript
// Comprehensive Theme System
interface ThemeDefinition {
  id: string;
  name: string;
  type: 'light' | 'dark' | 'high-contrast';
  colors: {
    // Editor colors
    background: string;
    foreground: string;
    selection: string;
    cursor: string;
    lineHighlight: string;
    
    // Syntax highlighting
    keyword: string;
    string: string;
    comment: string;
    number: string;
    operator: string;
    
    // UI colors
    primary: string;
    secondary: string;
    accent: string;
    border: string;
    surface: string;
  };
  typography: {
    fontFamily: string;
    fontSize: string;
    lineHeight: number;
    fontWeight: string;
  };
}

class ThemeManager {
  private currentTheme: ThemeDefinition;
  private presets: Map<string, ThemeDefinition> = new Map();
  private customThemes: Map<string, ThemeDefinition> = new Map();
  
  constructor() {
    this.initializePresets();
    this.loadThemePreferences();
    this.detectSystemPreference();
  }
  
  private initializePresets() {
    // Light theme preset
    this.presets.set('light', {
      id: 'light',
      name: 'Light',
      type: 'light',
      colors: {
        background: '#ffffff',
        foreground: '#24292f',
        selection: '#0969da1a',
        cursor: '#0969da',
        lineHighlight: '#f6f8fa',
        keyword: '#cf222e',
        string: '#032f62',
        comment: '#6a737d',
        number: '#0550ae',
        operator: '#24292f',
        primary: '#0969da',
        secondary: '#656d76',
        accent: '#1f883d',
        border: '#d1d9e0',
        surface: '#f6f8fa'
      },
      typography: {
        fontFamily: 'ui-monospace, SFMono-Regular, monospace',
        fontSize: '14px',
        lineHeight: 1.5,
        fontWeight: '400'
      }
    });
    
    // Dark theme preset
    this.presets.set('dark', {
      id: 'dark',
      name: 'Dark',
      type: 'dark',
      colors: {
        background: '#0d1117',
        foreground: '#e6edf3',
        selection: '#1f6feb4d',
        cursor: '#1f6feb',
        lineHighlight: '#21262d',
        keyword: '#ff7b72',
        string: '#a5d6ff',
        comment: '#8b949e',
        number: '#79c0ff',
        operator: '#e6edf3',
        primary: '#1f6feb',
        secondary: '#8b949e',
        accent: '#56d364',
        border: '#30363d',
        surface: '#21262d'
      },
      typography: {
        fontFamily: 'ui-monospace, SFMono-Regular, monospace',
        fontSize: '14px',
        lineHeight: 1.5,
        fontWeight: '400'
      }
    });
  }
  
  setTheme(themeId: string): void {
    const theme = this.presets.get(themeId) || this.customThemes.get(themeId);
    if (theme) {
      this.currentTheme = theme;
      this.applyTheme(theme);
      this.saveThemePreference(themeId);
    }
  }
  
  private applyTheme(theme: ThemeDefinition): void {
    // Apply CSS custom properties
    const root = document.documentElement;
    
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
    
    Object.entries(theme.typography).forEach(([key, value]) => {
      root.style.setProperty(`--typography-${key}`, String(value));
    });
    
    // Update theme class
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .concat(` theme-${theme.id}`);
  }
  
  createCustomTheme(baseThemeId: string, modifications: Partial<ThemeDefinition>): string {
    const baseTheme = this.presets.get(baseThemeId) || this.presets.get('light')!;
    const customId = `custom-${Date.now()}`;
    
    const customTheme: ThemeDefinition = {
      ...baseTheme,
      ...modifications,
      id: customId,
      colors: { ...baseTheme.colors, ...modifications.colors }
    };
    
    this.customThemes.set(customId, customTheme);
    this.saveCustomThemes();
    
    return customId;
  }
}
```

### User Preferences System
```typescript
// Comprehensive User Preferences
interface UserPreferences {
  theme: {
    editorTheme: string;
    previewTheme: string;
    autoDetectSystem: boolean;
    highContrastMode: boolean;
  };
  editor: {
    fontSize: number;
    fontFamily: string;
    lineHeight: number;
    showLineNumbers: boolean;
    wordWrap: boolean;
    tabSize: number;
    insertSpaces: boolean;
  };
  layout: {
    editorPreviewRatio: number; // 0.3 to 0.7
    showPreview: boolean;
    compactMode: boolean;
    sidebarPosition: 'left' | 'right';
  };
  preview: {
    stylePreset: string;
    customCSS: string;
    syncScrolling: boolean;
    showLineNumbers: boolean;
  };
  accessibility: {
    highContrastMode: boolean;
    reducedMotion: boolean;
    screenReaderOptimizations: boolean;
    largeText: boolean;
  };
}

class PreferenceManager {
  private preferences: UserPreferences;
  private readonly STORAGE_KEY = 'mdedit-preferences';
  
  constructor() {
    this.loadPreferences();
    this.applyPreferences();
  }
  
  updatePreference<T extends keyof UserPreferences>(
    section: T,
    key: keyof UserPreferences[T],
    value: UserPreferences[T][keyof UserPreferences[T]]
  ): void {
    (this.preferences[section] as any)[key] = value;
    this.savePreferences();
    this.applyPreferences();
  }
  
  exportPreferences(): string {
    return JSON.stringify(this.preferences, null, 2);
  }
  
  importPreferences(preferencesJson: string): boolean {
    try {
      const imported = JSON.parse(preferencesJson);
      this.validatePreferences(imported);
      this.preferences = imported;
      this.savePreferences();
      this.applyPreferences();
      return true;
    } catch (error) {
      console.error('Failed to import preferences:', error);
      return false;
    }
  }
  
  resetToDefaults(): void {
    this.preferences = this.getDefaultPreferences();
    this.savePreferences();
    this.applyPreferences();
  }
}
```

### Typography System
```typescript
// Font and Typography Management
interface FontDefinition {
  family: string;
  displayName: string;
  category: 'monospace' | 'serif' | 'sans-serif';
  variants: string[];
  webSafe: boolean;
}

class TypographyManager {
  private availableFonts: FontDefinition[] = [
    {
      family: 'ui-monospace, SFMono-Regular, monospace',
      displayName: 'System Monospace',
      category: 'monospace',
      variants: ['400', '500', '600', '700'],
      webSafe: true
    },
    {
      family: 'Consolas, Monaco, monospace',
      displayName: 'Consolas',
      category: 'monospace',
      variants: ['400', '700'],
      webSafe: true
    },
    {
      family: 'Fira Code, monospace',
      displayName: 'Fira Code',
      category: 'monospace',
      variants: ['300', '400', '500', '600', '700'],
      webSafe: false
    }
  ];
  
  async loadWebFont(fontFamily: string): Promise<void> {
    if (document.fonts && 'load' in document.fonts) {
      try {
        await document.fonts.load(`1em ${fontFamily}`);
        console.log(`Font loaded: ${fontFamily}`);
      } catch (error) {
        console.warn(`Failed to load font: ${fontFamily}`, error);
      }
    }
  }
  
  detectAvailableFonts(): Promise<string[]> {
    return new Promise((resolve) => {
      const testString = 'abcdefghijklmnopqrstuvwxyz0123456789';
      const testSize = '72px';
      const baseFonts = ['monospace', 'sans-serif', 'serif'];
      
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      
      // Measure base fonts
      const baseMeasurements = baseFonts.map(font => {
        context.font = `${testSize} ${font}`;
        return context.measureText(testString).width;
      });
      
      const availableFonts: string[] = [];
      
      this.availableFonts.forEach(font => {
        context.font = `${testSize} ${font.family}, ${font.category}`;
        const measurement = context.measureText(testString).width;
        
        const baselineIndex = font.category === 'monospace' ? 0 : 
                             font.category === 'sans-serif' ? 1 : 2;
        
        if (measurement !== baseMeasurements[baselineIndex]) {
          availableFonts.push(font.family);
        }
      });
      
      resolve(availableFonts);
    });
  }
}
```

### Preview Style System
```typescript
// Preview Styling and CSS Management
interface PreviewStyle {
  id: string;
  name: string;
  description: string;
  css: string;
  baseTheme: 'light' | 'dark' | 'auto';
}

class PreviewStyleManager {
  private styles: Map<string, PreviewStyle> = new Map();
  
  constructor() {
    this.initializePresets();
  }
  
  private initializePresets() {
    // GitHub style
    this.styles.set('github', {
      id: 'github',
      name: 'GitHub',
      description: 'GitHub-style markdown rendering',
      baseTheme: 'auto',
      css: `
        .preview-content {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
          line-height: 1.5;
          color: #24292f;
          background-color: #ffffff;
        }
        
        .preview-content h1, h2 {
          border-bottom: 1px solid #d0d7de;
          padding-bottom: 0.3em;
        }
        
        .preview-content pre {
          background-color: #f6f8fa;
          border-radius: 6px;
          font-size: 85%;
          line-height: 1.45;
          overflow: auto;
          padding: 16px;
        }
        
        .preview-content code {
          background-color: rgba(175, 184, 193, 0.2);
          border-radius: 3px;
          font-size: 85%;
          margin: 0;
          padding: 0.2em 0.4em;
        }
        
        .preview-content table {
          border-collapse: collapse;
          border-spacing: 0;
        }
        
        .preview-content table th,
        .preview-content table td {
          border: 1px solid #d0d7de;
          padding: 6px 13px;
        }
      `
    });
    
    // Minimal style
    this.styles.set('minimal', {
      id: 'minimal',
      name: 'Minimal',
      description: 'Clean, minimal styling',
      baseTheme: 'auto',
      css: `
        .preview-content {
          font-family: Georgia, 'Times New Roman', serif;
          line-height: 1.6;
          max-width: none;
          color: #333;
        }
        
        .preview-content h1, h2, h3, h4, h5, h6 {
          font-weight: 600;
          margin-top: 1.5em;
          margin-bottom: 0.5em;
        }
        
        .preview-content pre {
          background-color: #f8f8f8;
          border: 1px solid #e1e1e1;
          border-radius: 3px;
          font-family: Consolas, Monaco, monospace;
          font-size: 0.9em;
          padding: 1em;
          overflow-x: auto;
        }
        
        .preview-content blockquote {
          border-left: 4px solid #ddd;
          margin: 1em 0;
          padding-left: 1em;
          color: #666;
        }
      `
    });
  }
  
  applyStyle(styleId: string, previewElement: HTMLElement): void {
    const style = this.styles.get(styleId);
    if (!style) return;
    
    // Remove existing style
    const existingStyle = document.getElementById('preview-custom-style');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    // Apply new style
    const styleElement = document.createElement('style');
    styleElement.id = 'preview-custom-style';
    styleElement.textContent = style.css;
    document.head.appendChild(styleElement);
  }
  
  createCustomStyle(name: string, css: string): string {
    const id = `custom-${Date.now()}`;
    const customStyle: PreviewStyle = {
      id,
      name,
      description: 'Custom style',
      baseTheme: 'auto',
      css
    };
    
    this.styles.set(id, customStyle);
    return id;
  }
}
```

## Testing
### Testing Standards
- **Unit Tests:** Test theme application and preference management
- **Visual Tests:** Verify theme consistency across components
- **Accessibility Tests:** Test high contrast mode and WCAG compliance
- **Integration Tests:** Test preference persistence and import/export

### Key Test Scenarios
1. Theme switching between light and dark modes
2. System theme preference detection and auto-switching
3. Custom color scheme creation and application
4. Typography changes reflect in real-time
5. Layout preferences persist across sessions
6. Preview theme independence from editor theme
7. Import/export of preference configurations
8. High contrast mode accessibility compliance

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*