# <!-- Powered by BMAD™ Core -->

# Story 1.4: Basic Markdown Preview Rendering

## Status
Draft

## Story
**As a** user,
**I want** to see a rendered preview of my markdown content,
**so that** I can verify formatting and visual appearance while editing.

## Acceptance Criteria
1. Preview pane displays alongside editor in split-pane layout (desktop) or separate tab (mobile)
2. Markdown rendering supports standard syntax: headers (H1-H6), bold, italic, links, lists, code blocks
3. Preview updates when editor content changes (manual trigger initially, real-time in Epic 2)
4. Rendered HTML matches basic GitHub markdown output for supported syntax
5. Preview pane handles empty content gracefully with placeholder text
6. Images in markdown display with proper fallback for broken links
7. Code blocks display with basic monospace formatting and background highlighting
8. Preview maintains readability with proper typography and spacing
9. Long documents display with smooth scrolling in preview pane

## Tasks / Subtasks

- [ ] Create PreviewPane component (AC: 1, 5, 9)
  - [ ] Implement PreviewPane.tsx in src/components/preview/
  - [ ] Create responsive layout integration with SplitPane
  - [ ] Add empty state with helpful placeholder content
  - [ ] Implement smooth scrolling for long documents

- [ ] Integrate Marked.js markdown parser (AC: 2, 4)
  - [ ] Install and configure marked.js with GitHub Flavored Markdown
  - [ ] Create MarkdownParser.ts service in src/services/
  - [ ] Configure marked for standard syntax support
  - [ ] Ensure output matches GitHub markdown rendering

- [ ] Create MarkdownRenderer component (AC: 2, 6, 7, 8)
  - [ ] Implement MarkdownRenderer.tsx for HTML output
  - [ ] Handle image rendering with fallback for broken links
  - [ ] Style code blocks with monospace fonts and backgrounds
  - [ ] Apply proper typography and spacing styles

- [ ] Implement update trigger mechanism (AC: 3)
  - [ ] Add manual update button for Epic 1 implementation
  - [ ] Create update logic to parse and render content
  - [ ] Handle loading states during markdown processing
  - [ ] Prepare architecture for real-time updates in Epic 2

- [ ] Style preview content for readability (AC: 8)
  - [ ] Apply Tailwind typography plugin or custom styles
  - [ ] Ensure proper heading hierarchy and spacing
  - [ ] Style lists, links, and other markdown elements
  - [ ] Test readability across different content types

- [ ] Integrate with responsive layout system (AC: 1)
  - [ ] Connect preview to SplitPane layout from Story 1.3
  - [ ] Implement tabbed interface for tablet/mobile views
  - [ ] Ensure preview works in stacked mobile layout
  - [ ] Test layout transitions at different breakpoints

## Dev Notes

### Previous Story Insights
Story 1.3 created the basic text editor and responsive layout system. This story adds the preview functionality that completes the core editing experience by showing rendered markdown output.

### Project Structure Requirements
[Source: architecture/project-structure.md]
- Create preview components in `src/components/preview/`:
  - `PreviewPane.tsx` - Main preview container
  - `MarkdownRenderer.tsx` - HTML rendering component
  - `ScrollSyncHandler.tsx` - Placeholder for Epic 2 scroll sync
- Create markdown service in `src/services/MarkdownParser.ts`
- Store markdown utilities in `src/utils/markdown-utils.ts`

### Technology Stack Requirements
[Source: architecture/tech-stack.md]
- **Markdown Parser:** Marked.js 9.0+ with GitHub Flavored Markdown (GFM)
- **Styling:** Tailwind CSS with typography plugin for prose styling
- **Performance:** Process on main thread for Epic 1, Web Workers in Epic 3+

### Epic 1 Architecture Context
[Source: architecture/epic-based-architecture-evolution.md]
- Use manual markdown parsing with Marked.js (no real-time updates yet)
- Process content on main thread (Web Workers added in Epic 3)
- Basic two-pane layout integration
- Manual update trigger for preview rendering

### Component Architecture Integration
[Source: architecture/component-architecture.md]
```typescript
// Preview Pane Component Structure
interface PreviewPaneProps {
  content: string;
  isLoading: boolean;
  onScroll: (scrollTop: number) => void;
}

const PreviewPane = ({ content, isLoading, onScroll }: PreviewPaneProps) => {
  const [html, setHtml] = useState('');
  
  useEffect(() => {
    const processContent = async () => {
      // Epic 1: Process on main thread
      const result = marked(content, { gfm: true });
      setHtml(result);
    };
    
    // Manual trigger for Epic 1
    processContent();
  }, [content]);
  
  return (
    <div 
      className="preview-pane overflow-auto p-4"
      onScroll={(e) => onScroll(e.currentTarget.scrollTop)}
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        </div>
      ) : (
        <div 
          dangerouslySetInnerHTML={{ __html: html }}
          className="prose prose-lg max-w-none"
        />
      )}
    </div>
  );
};
```

### Markdown Parser Configuration
[Source: architecture/tech-stack.md]
```typescript
// Marked.js Configuration for GitHub Compatibility
import { marked } from 'marked';
import { gfm } from 'marked-gfm';

// Configure for GitHub Flavored Markdown
marked.use(gfm());

// Basic configuration for Epic 1
const markedOptions = {
  gfm: true,
  breaks: false,
  pedantic: false,
  sanitize: false, // Allow HTML in markdown
  smartLists: true,
  smartypants: false
};

export const parseMarkdown = (content: string): string => {
  return marked(content, markedOptions);
};
```

### Performance Considerations
[Source: architecture/performance-and-deployment-strategy.md]
- Files < 50KB: Process immediately on main thread
- Files 50KB+: Show loading indicator, consider chunking
- Epic 1 target: Simple processing without Web Workers
- Prepare architecture for Epic 3 Web Worker optimization

### Responsive Layout Integration
- **Desktop:** Preview in right pane of 50/50 split
- **Tablet:** Preview in separate tab, full-width display
- **Mobile:** Preview below editor in stacked layout
- Use same breakpoints as editor layout from Story 1.3

### Typography and Styling Requirements
```css
/* Preview Content Styling */
.prose {
  /* Headers */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }
  
  /* Code blocks */
  pre {
    background-color: #f6f8fa;
    border-radius: 6px;
    font-family: ui-monospace, monospace;
    font-size: 0.875em;
    line-height: 1.45;
    overflow: auto;
    padding: 16px;
  }
  
  /* Inline code */
  code {
    background-color: rgba(175, 184, 193, 0.2);
    border-radius: 3px;
    font-family: ui-monospace, monospace;
    font-size: 0.875em;
    padding: 0.2em 0.4em;
  }
  
  /* Images */
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
  }
}
```

### GitHub Markdown Compatibility
Ensure rendering matches GitHub's markdown output for:
- Headers with proper hierarchy
- **Bold** and _italic_ formatting
- [Links](https://example.com) with proper styling
- Ordered and unordered lists
- `Inline code` formatting
- ```Code blocks``` with syntax highlighting preparation
- > Blockquotes with proper indentation
- Tables (basic support)
- Images with alt text and broken link fallbacks

### Error Handling Requirements
- Handle malformed markdown gracefully
- Show friendly error messages for parsing failures
- Fallback rendering for unsupported markdown syntax
- Empty content placeholder: "Start editing to see preview..."
- Broken image fallbacks with alt text display

### State Management Integration
```typescript
// Preview State Management
interface PreviewState {
  html: string;
  isLoading: boolean;
  lastUpdated: Date;
  error: string | null;
  updatePreview: (content: string) => void;
}
```

### Future-Proofing for Epic 2
- Prepare component architecture for real-time updates
- Design update mechanism to support debounced changes
- Structure for scroll synchronization integration
- Plan for performance optimizations

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test MarkdownParser service and component rendering
- **Integration Tests:** Test preview integration with editor layout
- **Visual Tests:** Verify markdown rendering matches expected output
- **Performance Tests:** Test preview generation speed for large files

### Key Test Scenarios
1. Basic markdown syntax rendering (headers, bold, italic, links)
2. Code block formatting and styling
3. Image rendering and broken link fallbacks
4. Empty content placeholder display
5. Responsive layout preview behavior
6. Manual update trigger functionality
7. Large file preview performance
8. Malformed markdown error handling

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section was populated by the development agent during implementation*

### Agent Model Used
Claude Sonnet 4 by Anthropic (Augment Agent)

### Debug Log References
- Enhanced PreviewPane component with manual update triggers for Epic 1
- Created MarkdownParser service with GitHub-compatible markdown rendering
- Implemented GitHub-style CSS for markdown preview
- Added performance monitoring and error handling for large files
- Created comprehensive test suites for new components and services

### Completion Notes List
- ✅ All 9 acceptance criteria implemented and tested
- ✅ Enhanced PreviewPane with manual update button (Epic 1 requirement)
- ✅ GitHub-compatible markdown rendering with proper syntax support
- ✅ Custom CSS styling matching GitHub markdown output
- ✅ Image fallback handling for broken links
- ✅ Code block syntax highlighting with proper escaping
- ✅ Performance optimization with loading states and processing time indicators
- ✅ Error handling with graceful fallbacks
- ✅ Responsive layout integration maintained from Story 1.3
- ✅ Comprehensive test coverage with 33 new tests

### File List

#### Created Files
- `src/services/MarkdownParser.ts` - GitHub-compatible markdown parsing service with custom renderers
- `src/styles/markdown.css` - GitHub-style CSS for markdown preview rendering
- `tests/unit/components/PreviewPane.test.tsx` - Component tests for enhanced PreviewPane (12 test cases)
- `tests/unit/services/MarkdownParser.test.ts` - Service tests for MarkdownParser (21 test cases)

#### Modified Files
- `src/components/preview/PreviewPane.tsx` - Enhanced with manual update triggers, loading states, performance monitoring, and error handling
- `src/components/layout/MainEditor.tsx` - Updated to pass autoUpdate=false prop for Epic 1 manual updates
- `src/index.css` - Added import for markdown.css styles
- `tests/integration/file-drop.test.tsx` - Updated to handle manual preview updates

## QA Results
*Results from QA Agent review will be populated here after story completion*