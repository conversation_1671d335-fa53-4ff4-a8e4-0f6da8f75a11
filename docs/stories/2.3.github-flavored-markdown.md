# <!-- Powered by BMAD™ Core -->

# Story 2.3: GitHub Flavored Markdown Rendering

## Status
Draft

## Story
**As a** user,
**I want** my preview to match exactly what I'll see on GitHub,
**so that** I can confidently prepare content for GitHub repositories and documentation.

## Acceptance Criteria
1. Full GitHub Flavored Markdown (GFM) support: tables, strikethrough, task lists, autolinks
2. Fenced code blocks with language-specific syntax highlighting in preview
3. HTML rendering matches GitHub's markdown processor output exactly
4. Table rendering with proper borders, alignment, and responsive behavior
5. Task lists ([x] and [ ]) render as interactive checkboxes (display only, not editable)
6. Emoji support using standard GitHub emoji shortcodes (:smile:, :+1:, etc.)
7. Automatic link detection for URLs and email addresses
8. Line breaks and paragraph handling matches GFM specifications exactly

## Tasks / Subtasks

- [ ] Configure advanced GFM parser (AC: 1, 3)
  - [ ] Upgrade marked.js configuration with full GFM extension
  - [ ] Install marked-gfm plugin for complete GitHub compatibility
  - [ ] Configure parser options to match GitHub's output exactly
  - [ ] Test against GitHub's markdown rendering for consistency

- [ ] Implement table rendering system (AC: 4)
  - [ ] Create responsive table styling with Tailwind CSS
  - [ ] Support column alignment (left, center, right)
  - [ ] Handle table overflow with horizontal scrolling
  - [ ] Test table rendering with various content types

- [ ] Add task list support (AC: 5)
  - [ ] Render checkboxes for task list items
  - [ ] Style checked and unchecked states appropriately
  - [ ] Ensure checkboxes are display-only (not interactive)
  - [ ] Test nested task lists and mixed content

- [ ] Integrate syntax highlighting for code blocks (AC: 2)
  - [ ] Install Prism.js or highlight.js for code highlighting
  - [ ] Configure language detection for fenced code blocks
  - [ ] Style code blocks to match GitHub's appearance
  - [ ] Support common programming languages and markup

- [ ] Implement emoji support (AC: 6)
  - [ ] Add emoji parser for GitHub shortcodes
  - [ ] Include emoji font/icon support
  - [ ] Test common emoji shortcodes used on GitHub
  - [ ] Ensure emoji rendering matches GitHub's display

- [ ] Configure autolink detection (AC: 7)
  - [ ] Enable automatic URL detection and linking
  - [ ] Support email address auto-linking
  - [ ] Configure link styling to match GitHub
  - [ ] Test various URL formats and edge cases

- [ ] Validate GFM specification compliance (AC: 8)
  - [ ] Test line break handling (soft vs hard breaks)
  - [ ] Verify paragraph spacing and formatting
  - [ ] Test HTML entity handling
  - [ ] Compare output against GitHub's rendered examples

## Dev Notes

### Previous Story Insights
Story 2.2 implemented real-time preview synchronization. This story enhances the preview rendering to achieve pixel-perfect GitHub compatibility, ensuring users can confidently prepare content for GitHub platforms.

### GitHub Flavored Markdown Configuration
[Source: architecture/tech-stack.md]
```typescript
// Enhanced GFM Configuration
import { marked } from 'marked';
import { gfm } from 'marked-gfm';
import { mangle } from 'marked-mangle';
import { markedHighlight } from 'marked-highlight';
import hljs from 'highlight.js';

// Configure marked for GitHub compatibility
marked.use(gfm());
marked.use(mangle());
marked.use(markedHighlight({
  langPrefix: 'hljs language-',
  highlight(code, lang) {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  }
}));

const gfmOptions = {
  gfm: true,
  breaks: false, // GitHub doesn't enable breaks by default
  pedantic: false,
  sanitize: false,
  smartLists: true,
  smartypants: false, // GitHub doesn't use smartypants
  tables: true,
  taskLists: true,
  strikethrough: true
};
```

### Table Rendering Styles
```css
/* GitHub-compatible table styling */
.preview-content table {
  border-collapse: collapse;
  border-spacing: 0;
  margin: 16px 0;
  overflow-x: auto;
  width: 100%;
}

.preview-content table th,
.preview-content table td {
  border: 1px solid #d0d7de;
  padding: 6px 13px;
}

.preview-content table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.preview-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* Column alignment */
.preview-content table td.text-left { text-align: left; }
.preview-content table td.text-center { text-align: center; }
.preview-content table td.text-right { text-align: right; }

/* Responsive table wrapper */
.table-wrapper {
  overflow-x: auto;
  margin: 16px 0;
}
```

### Task List Implementation
```typescript
// Task List Renderer
const taskListRenderer = {
  listitem(text: string) {
    const isTask = /^\[[ x]\] /.test(text);
    if (isTask) {
      const isChecked = /^\[x\] /.test(text);
      const content = text.replace(/^\[[ x]\] /, '');
      return `<li class="task-list-item">
        <input type="checkbox" class="task-list-item-checkbox" ${isChecked ? 'checked' : ''} disabled>
        ${content}
      </li>`;
    }
    return `<li>${text}</li>`;
  },
  
  list(body: string, ordered: boolean) {
    const hasTaskItems = body.includes('task-list-item');
    const className = hasTaskItems ? 'task-list' : '';
    const tag = ordered ? 'ol' : 'ul';
    return `<${tag} class="${className}">${body}</${tag}>`;
  }
};

marked.use({ renderer: taskListRenderer });
```

### Emoji Support Implementation
```typescript
// GitHub Emoji Support
import emojiRegex from 'emoji-regex';
import { githubEmojis } from 'github-emoji-map';

const emojiRenderer = {
  text(text: string) {
    // Replace GitHub emoji shortcodes
    text = text.replace(/:([a-z0-9_+-]+):/g, (match, emojiName) => {
      const emoji = githubEmojis[emojiName];
      return emoji ? `<span class="emoji">${emoji}</span>` : match;
    });
    
    // Handle native emoji
    const regex = emojiRegex();
    text = text.replace(regex, (emoji) => {
      return `<span class="emoji">${emoji}</span>`;
    });
    
    return text;
  }
};
```

### Syntax Highlighting Configuration
```typescript
// Code Block Syntax Highlighting
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; // GitHub-style highlighting

const highlightRenderer = {
  code(code: string, language: string | undefined) {
    if (language && hljs.getLanguage(language)) {
      try {
        const highlighted = hljs.highlight(code, { language }).value;
        return `<pre><code class="hljs language-${language}">${highlighted}</code></pre>`;
      } catch (err) {
        console.warn(`Syntax highlighting failed for language: ${language}`, err);
      }
    }
    
    // Fallback to plain code block
    const escaped = code.replace(/[&<>"']/g, (char) => {
      const entities: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;'
      };
      return entities[char];
    });
    
    return `<pre><code>${escaped}</code></pre>`;
  }
};
```

### Autolink Configuration
```typescript
// Enhanced Autolink Detection
const autolinkRenderer = {
  text(text: string) {
    // URL detection (GitHub-compatible pattern)
    const urlRegex = /(https?:\/\/[^\s<>()]+)/gi;
    text = text.replace(urlRegex, (url) => {
      return `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`;
    });
    
    // Email detection
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi;
    text = text.replace(emailRegex, (email) => {
      return `<a href="mailto:${email}">${email}</a>`;
    });
    
    return text;
  }
};
```

### Complete GFM Parser Integration
```typescript
// Complete GitHub Flavored Markdown Parser
class GitHubMarkdownParser {
  private marked: typeof marked;
  
  constructor() {
    this.marked = marked;
    this.configureGFM();
  }
  
  private configureGFM() {
    // Use GFM extension
    this.marked.use(gfm({
      pedantic: false,
      gfm: true,
      breaks: false,
      sanitize: false,
      smartLists: true,
      smartypants: false,
      xhtml: false
    }));
    
    // Add custom renderers
    this.marked.use({ 
      renderer: {
        ...taskListRenderer,
        ...emojiRenderer,
        ...highlightRenderer,
        ...autolinkRenderer
      }
    });
  }
  
  parse(content: string): string {
    try {
      return this.marked.parse(content);
    } catch (error) {
      console.error('GFM parsing error:', error);
      return `<div class="parsing-error">Error parsing markdown: ${error.message}</div>`;
    }
  }
  
  // GitHub compatibility test
  async testGitHubCompatibility(testCases: string[]): Promise<boolean> {
    // Compare our output with GitHub's API if needed
    for (const testCase of testCases) {
      const ourOutput = this.parse(testCase);
      // Could integrate with GitHub API for comparison
      console.log('Parsed:', testCase, '->', ourOutput);
    }
    return true;
  }
}
```

### GitHub Compatibility Test Cases
```typescript
// Test cases for GitHub compatibility
const gfmTestCases = [
  // Tables
  `| Header 1 | Header 2 | Header 3 |
|----------|:--------:|---------:|
| Left     | Center   | Right    |
| Content  | More     | Data     |`,
  
  // Task lists
  `- [x] Completed task
- [ ] Incomplete task
- [x] Another completed task
  - [ ] Nested task
  - [x] Nested completed`,
  
  // Strikethrough
  `~~This text is struck through~~`,
  
  // Autolinks
  `Visit https://github.com <NAME_EMAIL>`,
  
  // Emoji
  `:smile: :+1: :rocket: :octocat:`,
  
  // Code blocks with syntax highlighting
  `\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\``,
  
  // Mixed content
  `# GitHub Features

This is a **bold** statement with ~~strikethrough~~ text.

| Feature | Support | Notes |
|---------|:-------:|-------|
| Tables  | ✅      | Full  |
| Tasks   | ✅      | Read-only |

- [x] Implement tables
- [ ] Add emoji support :rocket:

Visit https://github.com for more info!`
];
```

### Preview Styling for GitHub Compatibility
```css
/* GitHub-compatible preview styles */
.preview-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #24292f;
  background-color: #ffffff;
}

/* Headings */
.preview-content h1, h2, h3, h4, h5, h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.preview-content h1 { border-bottom: 1px solid #d0d7de; font-size: 2em; padding-bottom: 0.3em; }
.preview-content h2 { border-bottom: 1px solid #d0d7de; font-size: 1.5em; padding-bottom: 0.3em; }

/* Code styling */
.preview-content code {
  background-color: rgba(175, 184, 193, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 6px;
  font-size: 85%;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

.preview-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  overflow: auto;
  padding: 16px;
}

/* Task lists */
.preview-content .task-list {
  list-style-type: none;
  padding-left: 0;
}

.preview-content .task-list-item {
  position: relative;
  list-style-type: none;
}

.preview-content .task-list-item-checkbox {
  margin-right: 0.5em;
  margin-left: -1.6em;
  margin-top: 0.15em;
  vertical-align: middle;
}

/* Emoji */
.preview-content .emoji {
  font-style: normal;
  font-size: 1.2em;
  vertical-align: -0.1em;
}
```

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test GFM parser configuration and individual feature rendering
- **Compatibility Tests:** Compare output against GitHub's rendering
- **Visual Tests:** Verify styling matches GitHub's appearance
- **Integration Tests:** Test GFM features with real-time preview updates

### Key Test Scenarios
1. Table rendering with various alignments and content
2. Task list checkbox rendering and styling
3. Fenced code block syntax highlighting
4. Emoji shortcode conversion to visual emoji
5. Automatic URL and email linking
6. Strikethrough text formatting
7. Mixed content with multiple GFM features
8. GitHub compatibility across common markdown patterns

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*