# <!-- Powered by BMAD™ Core -->

# Story 2.1: CodeMirror Integration with Syntax Highlighting

## Status
Draft

## Story
**As a** user,
**I want** a professional code editor with syntax highlighting for markdown,
**so that** I can edit with the same quality tools I use for programming.

## Acceptance Criteria
1. CodeMirror 6 editor replaces basic textarea with full feature compatibility
2. Markdown syntax highlighting displays headers, bold, italic, links, and code blocks with distinct colors
3. Line numbers display optionally (user preference) with proper alignment
4. Code folding available for long code blocks and sections
5. Editor supports all standard text editing operations: select, copy, cut, paste, undo, redo
6. Search and replace functionality with regex support and keyboard shortcuts (Ctrl+F, Ctrl+H)
7. Auto-indentation and smart bracket matching for markdown formatting
8. Editor performance remains smooth with documents up to 200KB
9. Custom markdown highlighting theme matches overall application design

## Tasks / Subtasks

- [ ] Install and configure CodeMirror 6 dependencies (AC: 1)
  - [ ] Install @codemirror/state, @codemirror/view, @codemirror/lang-markdown packages
  - [ ] Install @codemirror/commands, @codemirror/search for editor features
  - [ ] Configure TypeScript definitions for CodeMirror integration
  - [ ] Set up bundle optimization to prevent bloating Epic 1 builds

- [ ] Create AdvancedEditor component replacing BasicEditor (AC: 1, 5)
  - [ ] Implement AdvancedEditor.tsx in src/components/editor/
  - [ ] Create CodeMirrorWrapper.tsx for React integration
  - [ ] Implement proper controlled component state management
  - [ ] Ensure backward compatibility with existing editor interface

- [ ] Implement markdown syntax highlighting (AC: 2, 9)
  - [ ] Configure @codemirror/lang-markdown extension
  - [ ] Create custom markdown highlighting theme
  - [ ] Style headers (H1-H6) with distinct colors and font weights
  - [ ] Highlight bold, italic, links, and code blocks appropriately
  - [ ] Match theme colors with application design system

- [ ] Add optional line numbers and code folding (AC: 3, 4)
  - [ ] Implement line numbers with proper alignment and styling
  - [ ] Add user preference toggle for line number display
  - [ ] Configure code folding for markdown code blocks and sections
  - [ ] Ensure folding works with both fenced and indented code blocks

- [ ] Integrate advanced editing features (AC: 6, 7)
  - [ ] Install and configure @codemirror/search for find/replace
  - [ ] Implement Ctrl+F and Ctrl+H keyboard shortcuts
  - [ ] Add regex support for search and replace operations
  - [ ] Configure auto-indentation for markdown formatting
  - [ ] Implement smart bracket matching for parentheses and brackets

- [ ] Optimize performance for large documents (AC: 8)
  - [ ] Test editor performance with documents up to 200KB
  - [ ] Configure viewport-based rendering for long documents
  - [ ] Implement efficient syntax highlighting for large files
  - [ ] Monitor memory usage and rendering performance

- [ ] Integrate with Epic Detection System (AC: 1)
  - [ ] Update App component to detect CodeMirror availability
  - [ ] Implement progressive enhancement from BasicEditor to AdvancedEditor
  - [ ] Handle fallback scenarios for environments without CodeMirror
  - [ ] Test Epic 1 vs Epic 2+ editor selection logic

## Dev Notes

### Previous Story Insights
Epic 1 established the basic textarea editor and responsive layout. This story upgrades to CodeMirror 6, providing professional editing capabilities while maintaining backward compatibility with the established interface.

### Project Structure Integration
[Source: architecture/project-structure.md]
- Create advanced editor components in `src/components/editor/`:
  - `AdvancedEditor.tsx` - Main CodeMirror-based editor
  - `CodeMirrorWrapper.tsx` - React integration wrapper
  - `SearchOverlay.tsx` - Search and replace interface
- Update existing components to support both editor types
- Store editor configurations in `src/services/EditorConfig.ts`

### Epic 2 Architecture Requirements
[Source: architecture/epic-based-architecture-evolution.md]
```typescript
// CodeMirror Integration Architecture
interface AdvancedEditor {
  value: string;
  onChange: (value: string) => void;
  extensions: Extension[];
  theme: 'light' | 'dark';
  onScroll?: (scrollInfo: ScrollInfo) => void;
}

// Required CodeMirror Extensions
const editorExtensions = [
  markdown(),
  syntaxHighlighting(defaultHighlightStyle),
  lineNumbers(),
  foldGutter(),
  bracketMatching(),
  searchKeymap,
  historyKeymap,
  defaultKeymap
];
```

### Component Architecture Integration
[Source: architecture/component-architecture.md]
```typescript
// Epic 2 Advanced Editor Implementation
const AdvancedEditor = ({ value, onChange, onScroll }: AdvancedEditorProps) => {
  const extensions = useMemo(() => [
    markdown(),
    syntaxHighlighting(defaultHighlightStyle),
    lineNumbers(),
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        onChange(update.state.doc.toString());
      }
      if (update.scrolled) {
        onScroll?.(update.view);
      }
    })
  ], [onChange, onScroll]);

  return (
    <CodeMirror
      value={value}
      extensions={extensions}
      theme={theme === 'dark' ? oneDark : undefined}
    />
  );
};

// Progressive Epic Detection
const detectAvailableEpic = (): number => {
  if (typeof Worker !== 'undefined' && 'serviceWorker' in navigator) return 4;
  if (window.CodeMirror || import.meta.env.VITE_EPIC_LEVEL >= 3) return 3;
  if (import.meta.env.VITE_EPIC_LEVEL >= 2) return 2;
  return 1;
};
```

### Technology Stack Integration
[Source: architecture/tech-stack.md]
- **Editor Component:** CodeMirror 6.0+ for advanced editing features
- **Extensions:** Markdown language support, syntax highlighting, search functionality
- **Bundling:** Lazy loading to prevent bloating Epic 1 builds
- **Performance:** Viewport-based rendering for large documents

### CodeMirror 6 Configuration
```typescript
// CodeMirror 6 Setup
import { EditorState, Extension } from '@codemirror/state';
import { EditorView, keymap, ViewUpdate } from '@codemirror/view';
import { defaultKeymap, history, historyKeymap } from '@codemirror/commands';
import { searchKeymap, highlightSelectionMatches } from '@codemirror/search';
import { autocompletion, completionKeymap, closeBrackets, closeBracketsKeymap } from '@codemirror/autocomplete';
import { foldGutter, indentOnInput, bracketMatching } from '@codemirror/language';
import { defaultHighlightStyle, syntaxHighlighting } from '@codemirror/language';
import { markdown } from '@codemirror/lang-markdown';
import { lineNumbers, highlightActiveLineGutter } from '@codemirror/view';

const createEditorExtensions = (preferences: EditorPreferences): Extension[] => [
  // Core functionality
  history(),
  indentOnInput(),
  bracketMatching(),
  closeBrackets(),
  autocompletion(),
  highlightSelectionMatches(),
  
  // Language support
  markdown(),
  syntaxHighlighting(defaultHighlightStyle),
  
  // UI features
  ...(preferences.showLineNumbers ? [lineNumbers(), highlightActiveLineGutter()] : []),
  ...(preferences.codeFolding ? [foldGutter()] : []),
  
  // Keymaps
  keymap.of([
    ...closeBracketsKeymap,
    ...defaultKeymap,
    ...searchKeymap,
    ...historyKeymap,
    ...completionKeymap,
  ])
];
```

### Markdown Syntax Highlighting Theme
```typescript
// Custom Markdown Highlighting Theme
import { HighlightStyle, syntaxHighlighting } from '@codemirror/language';
import { tags } from '@lezer/highlight';

const markdownHighlightStyle = HighlightStyle.define([
  // Headers
  { tag: tags.heading1, fontSize: '1.5em', fontWeight: 'bold', color: '#1f2937' },
  { tag: tags.heading2, fontSize: '1.3em', fontWeight: 'bold', color: '#374151' },
  { tag: tags.heading3, fontSize: '1.2em', fontWeight: 'bold', color: '#4b5563' },
  { tag: tags.heading4, fontSize: '1.1em', fontWeight: 'bold', color: '#6b7280' },
  { tag: tags.heading5, fontSize: '1.0em', fontWeight: 'bold', color: '#6b7280' },
  { tag: tags.heading6, fontSize: '1.0em', fontWeight: 'bold', color: '#9ca3af' },
  
  // Text formatting
  { tag: tags.strong, fontWeight: 'bold', color: '#1f2937' },
  { tag: tags.emphasis, fontStyle: 'italic', color: '#374151' },
  { tag: tags.strikethrough, textDecoration: 'line-through', color: '#6b7280' },
  
  // Code and links
  { tag: tags.monospace, fontFamily: 'ui-monospace, monospace', backgroundColor: '#f3f4f6', color: '#dc2626' },
  { tag: tags.link, color: '#2563eb', textDecoration: 'underline' },
  { tag: tags.url, color: '#2563eb' },
  
  // Lists and quotes
  { tag: tags.list, color: '#374151' },
  { tag: tags.quote, color: '#6b7280', borderLeft: '4px solid #d1d5db', paddingLeft: '1em' },
]);
```

### Performance Optimization Strategies
[Source: architecture/performance-and-deployment-strategy.md]
- **Lazy Loading:** Load CodeMirror only when Epic 2+ detected
- **Viewport Rendering:** Handle large documents efficiently
- **Bundle Splitting:** Separate CodeMirror bundle from core application
- **Memory Management:** Proper cleanup of editor instances

### Bundle Optimization Configuration
```typescript
// vite.config.ts CodeMirror optimization
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          codemirror: [
            '@codemirror/state',
            '@codemirror/view', 
            '@codemirror/lang-markdown',
            '@codemirror/commands',
            '@codemirror/search'
          ]
        }
      }
    }
  }
});
```

### User Preferences Integration
```typescript
// Editor Preferences Store
interface EditorPreferences {
  showLineNumbers: boolean;
  codeFolding: boolean;
  theme: 'light' | 'dark';
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  wordWrap: boolean;
}

const useEditorPreferences = () => {
  const [preferences, setPreferences] = useState<EditorPreferences>({
    showLineNumbers: true,
    codeFolding: true,
    theme: 'light',
    fontSize: 14,
    fontFamily: 'ui-monospace, monospace',
    tabSize: 2,
    wordWrap: true
  });

  // Persistence logic with localStorage
  useEffect(() => {
    const stored = localStorage.getItem('editor-preferences');
    if (stored) {
      setPreferences(JSON.parse(stored));
    }
  }, []);

  const updatePreference = <K extends keyof EditorPreferences>(
    key: K, 
    value: EditorPreferences[K]
  ) => {
    const updated = { ...preferences, [key]: value };
    setPreferences(updated);
    localStorage.setItem('editor-preferences', JSON.stringify(updated));
  };

  return { preferences, updatePreference };
};
```

### Search and Replace Implementation
```typescript
// Search and Replace Integration
import { searchPanelOpen, findNext, findPrevious, replaceAll } from '@codemirror/search';

const searchExtensions = [
  searchKeymap,
  // Custom search commands
  keymap.of([
    { key: 'Ctrl-f', run: () => { /* open search panel */ } },
    { key: 'Ctrl-h', run: () => { /* open replace panel */ } },
    { key: 'F3', run: findNext },
    { key: 'Shift-F3', run: findPrevious },
  ])
];
```

### Testing Strategy for CodeMirror Integration
- **Unit Tests:** Test editor component mounting and basic operations
- **Integration Tests:** Verify syntax highlighting and keyboard shortcuts
- **Performance Tests:** Measure rendering performance with large documents
- **E2E Tests:** Test complete editing workflows with CodeMirror

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test CodeMirror component integration and extension configuration
- **Performance Tests:** Verify smooth operation with 200KB documents
- **Accessibility Tests:** Ensure CodeMirror maintains keyboard navigation
- **Cross-browser Tests:** Validate CodeMirror behavior across all supported browsers

### Key Test Scenarios
1. CodeMirror editor renders with proper syntax highlighting
2. Line numbers toggle correctly with user preferences
3. Search and replace functionality works with keyboard shortcuts
4. Code folding operates correctly for markdown sections
5. Large document performance remains smooth
6. Theme switching applies correctly to editor
7. Epic detection properly upgrades from BasicEditor
8. All standard text operations work identically to textarea

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled during development*

### Debug Log References
*To be filled during development*

### Completion Notes List
*To be filled during development*

### File List
*To be filled during development*

## QA Results
*Results from QA Agent review will be populated here after story completion*