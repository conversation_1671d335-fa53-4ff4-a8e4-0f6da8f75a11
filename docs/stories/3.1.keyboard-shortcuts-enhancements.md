# <!-- Powered by BMAD™ Core -->

# Story 3.1: Comprehensive Keyboard Shortcuts and Editor Enhancements

## Status
Draft

## Story
**As a** power user,
**I want** full IDE-style keyboard shortcuts and advanced editing features,
**so that** I can work efficiently without reaching for the mouse.

## Acceptance Criteria
1. Complete keyboard shortcut support: Ctrl+N (new), Ctrl+O (open), Ctrl+S (save), Ctrl+Z (undo), Ctrl+Y (redo)
2. Advanced text manipulation: Ctrl+D (duplicate line), Ctrl+L (select line), Alt+Up/Down (move line)
3. Search and navigation: Ctrl+F (find), Ctrl+H (replace), Ctrl+G (go to line), F3 (find next)
4. Multi-cursor support with Ctrl+click for simultaneous editing at multiple positions
5. Block selection and editing with Alt+drag for column-based text manipulation
6. Word wrap toggle (Alt+Z) and line numbers toggle for user preference
7. Vim keybindings option for users preferring modal editing
8. Customizable keyboard shortcuts with user-defined key combinations
9. Command palette (Ctrl+Shift+P) for discovering and executing commands

## Tasks / Subtasks

- [ ] Implement comprehensive keyboard shortcuts (AC: 1, 2, 3)
  - [ ] Create KeyboardShortcutManager service in src/services/
  - [ ] Implement file operations: new, open, save shortcuts
  - [ ] Add text manipulation shortcuts: duplicate line, select line, move line
  - [ ] Integrate search shortcuts with existing search functionality

- [ ] Add multi-cursor editing support (AC: 4)
  - [ ] Configure CodeMirror multi-cursor extensions
  - [ ] Implement Ctrl+click multi-cursor placement
  - [ ] Add keyboard shortcuts for multi-cursor operations
  - [ ] Test multi-cursor functionality with various editing operations

- [ ] Implement block selection and column editing (AC: 5)
  - [ ] Configure CodeMirror rectangular selection
  - [ ] Implement Alt+drag block selection
  - [ ] Add column-based editing capabilities
  - [ ] Test block selection with different content types

- [ ] Create user preference toggles (AC: 6)
  - [ ] Implement word wrap toggle with Alt+Z shortcut
  - [ ] Add line numbers toggle functionality
  - [ ] Create preference persistence system
  - [ ] Add UI controls for preference management

- [ ] Add Vim keybindings support (AC: 7)
  - [ ] Install and configure CodeMirror Vim extension
  - [ ] Implement Vim mode toggle
  - [ ] Add Vim status indicator in editor
  - [ ] Test common Vim operations and commands

- [ ] Create customizable shortcut system (AC: 8)
  - [ ] Implement shortcut customization interface
  - [ ] Create shortcut conflict detection
  - [ ] Add import/export for shortcut configurations
  - [ ] Validate custom key combinations

- [ ] Implement command palette (AC: 9)
  - [ ] Create CommandPalette component
  - [ ] Add command discovery and search functionality
  - [ ] Implement command execution system
  - [ ] Add fuzzy search for command names

## Dev Notes

### Previous Story Insights
Epic 2 established professional editing capabilities with CodeMirror integration. This story enhances the editor with power-user features, making MDEdit competitive with professional IDEs for markdown editing.

### Keyboard Shortcut Architecture
```typescript
// Comprehensive Keyboard Shortcut System
interface KeyboardShortcut {
  key: string;
  command: string;
  description: string;
  category: 'file' | 'edit' | 'search' | 'view' | 'custom';
  handler: () => void | Promise<void>;
  enabled: boolean;
  customizable: boolean;
}

class KeyboardShortcutManager {
  private shortcuts = new Map<string, KeyboardShortcut>();
  private customShortcuts = new Map<string, KeyboardShortcut>();
  
  constructor() {
    this.initializeDefaultShortcuts();
    this.loadCustomShortcuts();
  }
  
  private initializeDefaultShortcuts() {
    // File operations
    this.registerShortcut({
      key: 'Ctrl+N',
      command: 'file.new',
      description: 'New file',
      category: 'file',
      handler: () => this.handleNewFile(),
      enabled: true,
      customizable: true
    });
    
    this.registerShortcut({
      key: 'Ctrl+O',
      command: 'file.open',
      description: 'Open file',
      category: 'file',
      handler: () => this.handleOpenFile(),
      enabled: true,
      customizable: true
    });
    
    this.registerShortcut({
      key: 'Ctrl+S',
      command: 'file.save',
      description: 'Save file',
      category: 'file',
      handler: () => this.handleSaveFile(),
      enabled: true,
      customizable: true
    });
    
    // Text manipulation
    this.registerShortcut({
      key: 'Ctrl+D',
      command: 'edit.duplicateLine',
      description: 'Duplicate line',
      category: 'edit',
      handler: () => this.handleDuplicateLine(),
      enabled: true,
      customizable: true
    });
    
    this.registerShortcut({
      key: 'Ctrl+L',
      command: 'edit.selectLine',
      description: 'Select line',
      category: 'edit',
      handler: () => this.handleSelectLine(),
      enabled: true,
      customizable: true
    });
    
    this.registerShortcut({
      key: 'Alt+Up',
      command: 'edit.moveLineUp',
      description: 'Move line up',
      category: 'edit',
      handler: () => this.handleMoveLineUp(),
      enabled: true,
      customizable: true
    });
    
    this.registerShortcut({
      key: 'Alt+Down',
      command: 'edit.moveLineDown',
      description: 'Move line down',
      category: 'edit',
      handler: () => this.handleMoveLineDown(),
      enabled: true,
      customizable: true
    });
  }
  
  registerShortcut(shortcut: KeyboardShortcut) {
    this.shortcuts.set(shortcut.key, shortcut);
  }
  
  executeCommand(key: string): boolean {
    const shortcut = this.shortcuts.get(key) || this.customShortcuts.get(key);
    if (shortcut && shortcut.enabled) {
      shortcut.handler();
      return true;
    }
    return false;
  }
  
  customizeShortcut(command: string, newKey: string): boolean {
    // Validate new key combination
    if (this.isKeyConflict(newKey, command)) {
      return false;
    }
    
    const existingShortcut = Array.from(this.shortcuts.values())
      .find(s => s.command === command);
    
    if (existingShortcut && existingShortcut.customizable) {
      const customShortcut = { ...existingShortcut, key: newKey };
      this.customShortcuts.set(newKey, customShortcut);
      this.saveCustomShortcuts();
      return true;
    }
    
    return false;
  }
}
```

### Multi-Cursor Implementation
```typescript
// Multi-Cursor Support with CodeMirror
import { EditorView, ViewPlugin, Decoration, DecorationSet } from '@codemirror/view';
import { StateField, StateEffect } from '@codemirror/state';

// Multi-cursor state management
const addCursor = StateEffect.define<{pos: number}>();
const removeCursor = StateEffect.define<{pos: number}>();

const multiCursorState = StateField.define<DecorationSet>({
  create() {
    return Decoration.none;
  },
  
  update(cursors, tr) {
    cursors = cursors.map(tr.changes);
    
    for (const effect of tr.effects) {
      if (effect.is(addCursor)) {
        const cursor = Decoration.widget({
          widget: new CursorWidget(),
          side: 0
        }).range(effect.value.pos);
        cursors = cursors.update({ add: [cursor] });
      } else if (effect.is(removeCursor)) {
        cursors = cursors.update({
          filter: (from, to) => from !== effect.value.pos
        });
      }
    }
    
    return cursors;
  },
  
  provide: f => EditorView.decorations.from(f)
});

// Multi-cursor plugin
const multiCursorPlugin = ViewPlugin.fromClass(class {
  constructor(private view: EditorView) {
    this.setupEventListeners();
  }
  
  private setupEventListeners() {
    this.view.dom.addEventListener('click', this.handleClick.bind(this));
  }
  
  private handleClick(event: MouseEvent) {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      const pos = this.view.posAtCoords({ x: event.clientX, y: event.clientY });
      if (pos !== null) {
        this.view.dispatch({
          effects: addCursor.of({ pos })
        });
      }
    }
  }
});
```

### Block Selection and Column Editing
```typescript
// Rectangular Selection Implementation
import { rectangularSelection, crosshairCursor } from '@codemirror/rectangular-selection';

const blockSelectionExtensions = [
  rectangularSelection(),
  crosshairCursor(),
  
  // Custom rectangular selection handling
  EditorView.domEventHandlers({
    mousedown(event, view) {
      if (event.altKey && event.button === 0) {
        // Start rectangular selection
        return handleRectangularSelection(event, view);
      }
      return false;
    }
  })
];

function handleRectangularSelection(event: MouseEvent, view: EditorView): boolean {
  const startPos = view.posAtCoords({ x: event.clientX, y: event.clientY });
  if (startPos === null) return false;
  
  let isDragging = true;
  
  const onMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    const endPos = view.posAtCoords({ x: e.clientX, y: e.clientY });
    if (endPos !== null) {
      // Update rectangular selection
      updateRectangularSelection(view, startPos, endPos);
    }
  };
  
  const onMouseUp = () => {
    isDragging = false;
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };
  
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
  
  return true;
}
```

### Vim Keybindings Integration
```typescript
// Vim Mode Support
import { vim, Vim } from '@codemirror/legacy-modes/mode/vim';

interface VimSettings {
  enabled: boolean;
  showStatusBar: boolean;
  customCommands: Map<string, string>;
}

class VimModeManager {
  private vimEnabled = false;
  private statusBar: HTMLElement | null = null;
  
  constructor(private view: EditorView) {
    this.setupVimMode();
  }
  
  private setupVimMode() {
    // Configure Vim extension
    const vimExtension = vim({
      status: true, // Show Vim status
      
      // Custom Vim commands
      commands: {
        'w': () => this.saveFile(),
        'q': () => this.closeFile(),
        'wq': () => this.saveAndClose(),
        'split': () => this.splitEditor(),
        'vsplit': () => this.verticalSplitEditor()
      }
    });
    
    return vimExtension;
  }
  
  toggleVimMode(): void {
    this.vimEnabled = !this.vimEnabled;
    
    if (this.vimEnabled) {
      this.enableVimMode();
    } else {
      this.disableVimMode();
    }
    
    this.updateStatusBar();
  }
  
  private enableVimMode(): void {
    // Reconfigure editor with Vim extensions
    this.view.dispatch({
      effects: StateEffect.appendConfig.of(vim())
    });
    
    this.createStatusBar();
  }
  
  private disableVimMode(): void {
    // Remove Vim extensions
    this.view.dispatch({
      effects: StateEffect.reconfigure.of([])
    });
    
    this.removeStatusBar();
  }
  
  private createStatusBar(): void {
    this.statusBar = document.createElement('div');
    this.statusBar.className = 'vim-status-bar';
    this.statusBar.innerHTML = 'NORMAL';
    
    const editorContainer = this.view.dom.parentElement;
    if (editorContainer) {
      editorContainer.appendChild(this.statusBar);
    }
  }
  
  private updateStatusBar(): void {
    if (this.statusBar && this.vimEnabled) {
      const vimState = (this.view.state as any).vim;
      if (vimState) {
        this.statusBar.innerHTML = vimState.mode.toUpperCase();
      }
    }
  }
}
```

### Command Palette Implementation
```typescript
// Command Palette Component
interface Command {
  id: string;
  name: string;
  description: string;
  category: string;
  keybinding?: string;
  handler: () => void | Promise<void>;
}

const CommandPalette: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [commands] = useState<Command[]>(getAllCommands());
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  const filteredCommands = useMemo(() => {
    if (!searchQuery) return commands;
    
    return commands.filter(command =>
      command.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      command.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [commands, searchQuery]);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setIsOpen(true);
      } else if (e.key === 'Escape' && isOpen) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);
  
  const handleCommandSelect = (command: Command) => {
    command.handler();
    setIsOpen(false);
    setSearchQuery('');
    setSelectedIndex(0);
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="command-palette-overlay">
      <div className="command-palette">
        <input
          type="text"
          placeholder="Search commands..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="command-search"
          autoFocus
        />
        
        <div className="command-list">
          {filteredCommands.map((command, index) => (
            <div
              key={command.id}
              className={`command-item ${index === selectedIndex ? 'selected' : ''}`}
              onClick={() => handleCommandSelect(command)}
            >
              <div className="command-info">
                <div className="command-name">{command.name}</div>
                <div className="command-description">{command.description}</div>
              </div>
              {command.keybinding && (
                <div className="command-keybinding">{command.keybinding}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Command registration system
function getAllCommands(): Command[] {
  return [
    {
      id: 'file.new',
      name: 'New File',
      description: 'Create a new markdown file',
      category: 'File',
      keybinding: 'Ctrl+N',
      handler: () => console.log('New file')
    },
    {
      id: 'editor.toggleLineNumbers',
      name: 'Toggle Line Numbers',
      description: 'Show or hide line numbers',
      category: 'Editor',
      handler: () => console.log('Toggle line numbers')
    },
    {
      id: 'view.togglePreview',
      name: 'Toggle Preview',
      description: 'Show or hide the preview pane',
      category: 'View',
      handler: () => console.log('Toggle preview')
    },
    // ... more commands
  ];
}
```

### Customizable Shortcuts Interface
```typescript
// Shortcut Customization Interface
const ShortcutCustomizer: React.FC = () => {
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const [editingShortcut, setEditingShortcut] = useState<string | null>(null);
  
  const handleShortcutEdit = (command: string, newKey: string) => {
    const shortcutManager = new KeyboardShortcutManager();
    const success = shortcutManager.customizeShortcut(command, newKey);
    
    if (success) {
      // Update local state
      setShortcuts(prev => prev.map(s => 
        s.command === command ? { ...s, key: newKey } : s
      ));
      setEditingShortcut(null);
    } else {
      // Show error message for conflict
      alert('Shortcut conflict detected. Please choose a different key combination.');
    }
  };
  
  return (
    <div className="shortcut-customizer">
      <h3>Keyboard Shortcuts</h3>
      
      {shortcuts.map(shortcut => (
        <div key={shortcut.command} className="shortcut-row">
          <div className="shortcut-info">
            <div className="shortcut-description">{shortcut.description}</div>
            <div className="shortcut-category">{shortcut.category}</div>
          </div>
          
          <div className="shortcut-key">
            {editingShortcut === shortcut.command ? (
              <KeybindingEditor
                initialKey={shortcut.key}
                onSave={(newKey) => handleShortcutEdit(shortcut.command, newKey)}
                onCancel={() => setEditingShortcut(null)}
              />
            ) : (
              <button
                className="shortcut-button"
                onClick={() => setEditingShortcut(shortcut.command)}
                disabled={!shortcut.customizable}
              >
                {shortcut.key}
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
```

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test keyboard shortcut handling and command execution
- **Integration Tests:** Test multi-cursor and block selection functionality
- **E2E Tests:** Test complete keyboard-driven workflows
- **Accessibility Tests:** Ensure keyboard navigation works for all users

### Key Test Scenarios
1. All default keyboard shortcuts execute correctly
2. Multi-cursor editing works with various text operations
3. Block selection and column editing function properly
4. Vim mode toggle works and maintains state
5. Command palette discovery and execution
6. Custom shortcut configuration and conflict detection
7. Word wrap and line number toggles
8. Cross-platform shortcut compatibility (Ctrl vs Cmd)

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*