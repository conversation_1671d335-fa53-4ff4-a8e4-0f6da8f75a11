# <!-- Powered by BMAD™ Core -->

# Story 3.4: Large File Performance Optimization with Web Workers

## Status
Draft

## Story
**As a** user,
**I want** to work smoothly with large markdown documents up to 200KB,
**so that** file size doesn't limit my productivity or editing experience.

## Acceptance Criteria
1. Web Worker implementation for markdown parsing of files 100KB and larger
2. Background processing maintains UI responsiveness during heavy parsing operations
3. Progressive loading and rendering for very large documents with visual feedback
4. Intelligent chunking strategies minimize memory usage for large files
5. Lazy loading of preview content based on viewport visibility
6. Performance monitoring with automatic fallback to simpler rendering for slow devices
7. Memory optimization prevents browser crashes or significant slowdown
8. File size warnings with performance impact explanations for user awareness
9. Benchmark testing confirms smooth operation with 200KB markdown files across supported browsers

## Tasks / Subtasks

- [ ] Enhance Web Worker system from Epic 2 (AC: 1, 2)
  - [ ] Upgrade markdown-worker.ts for improved large file handling
  - [ ] Implement advanced WorkerManager with better error recovery
  - [ ] Add worker pool management for concurrent processing
  - [ ] Create worker communication protocol optimization

- [ ] Implement progressive loading system (AC: 3)
  - [ ] Create ProgressiveLoader service in src/services/
  - [ ] Add chunked document processing with visual feedback
  - [ ] Implement incremental rendering pipeline
  - [ ] Add loading progress indicators and cancellation

- [ ] Add intelligent chunking strategies (AC: 4)
  - [ ] Create ChunkingStrategy service for optimal content splitting
  - [ ] Implement semantic chunking (by headers, paragraphs, code blocks)
  - [ ] Add memory-aware chunk size calculation
  - [ ] Create chunk priority system for important content first

- [ ] Implement lazy loading preview system (AC: 5)
  - [ ] Create VirtualizedPreview component from Epic 2 enhancement
  - [ ] Add viewport-based content rendering
  - [ ] Implement intersection observer for content visibility
  - [ ] Add placeholder rendering for off-screen content

- [ ] Create performance monitoring and fallback (AC: 6, 8)
  - [ ] Enhance PerformanceMonitor from Epic 2 with device detection
  - [ ] Add automatic performance mode switching
  - [ ] Implement file size warning system
  - [ ] Create performance impact explanations

- [ ] Optimize memory usage (AC: 7)
  - [ ] Implement advanced memory management strategies
  - [ ] Add garbage collection optimization hints
  - [ ] Create memory leak prevention system
  - [ ] Monitor and optimize DOM node management

- [ ] Create comprehensive benchmarking (AC: 9)
  - [ ] Build automated benchmark testing suite
  - [ ] Test 200KB file performance across all supported browsers
  - [ ] Create performance regression detection
  - [ ] Add continuous performance monitoring

## Dev Notes

### Enhanced Web Worker Architecture
```typescript
// Advanced Web Worker Pool Management
interface WorkerPoolConfig {
  maxWorkers: number;
  workerTimeoutMs: number;
  retryAttempts: number;
  fallbackToMainThread: boolean;
}

class WorkerPool {
  private workers: Worker[] = [];
  private availableWorkers: Worker[] = [];
  private taskQueue: WorkerTask[] = [];
  private activeConnections = new Map<string, WorkerConnection>();
  
  constructor(private config: WorkerPoolConfig) {
    this.initializeWorkerPool();
  }
  
  private initializeWorkerPool(): void {
    const workerCount = Math.min(
      this.config.maxWorkers,
      navigator.hardwareConcurrency || 2
    );
    
    for (let i = 0; i < workerCount; i++) {
      try {
        const worker = new Worker(
          new URL('../workers/markdown-worker.ts', import.meta.url),
          { type: 'module' }
        );
        
        worker.onmessage = this.handleWorkerMessage.bind(this);
        worker.onerror = this.handleWorkerError.bind(this);
        
        this.workers.push(worker);
        this.availableWorkers.push(worker);
      } catch (error) {
        console.warn(`Failed to create worker ${i}:`, error);
      }
    }
  }
  
  async processLargeDocument(
    content: string,
    options: ProcessingOptions
  ): Promise<string> {
    if (content.length < 100000) {
      // Use main thread for smaller files
      return this.processOnMainThread(content, options);
    }
    
    // Use worker pool for large files
    const chunks = this.createOptimalChunks(content);
    const worker = await this.acquireWorker();
    
    try {
      return await this.processChunkedContent(worker, chunks, options);
    } finally {
      this.releaseWorker(worker);
    }
  }
  
  private createOptimalChunks(content: string): DocumentChunk[] {
    const chunkingStrategy = new SemanticChunkingStrategy();
    return chunkingStrategy.createChunks(content, {
      maxChunkSize: 25000, // 25KB chunks for optimal processing
      minChunkSize: 5000,  // Minimum chunk size
      preserveStructure: true, // Keep markdown structure intact
      prioritizeHeaders: true  // Prioritize header-based splits
    });
  }
}
```

### Semantic Chunking Strategy
```typescript
// Intelligent Content Chunking
interface ChunkingOptions {
  maxChunkSize: number;
  minChunkSize: number;
  preserveStructure: boolean;
  prioritizeHeaders: boolean;
}

interface DocumentChunk {
  id: string;
  content: string;
  startLine: number;
  endLine: number;
  priority: number; // 1-10, higher = more important
  dependencies: string[]; // IDs of chunks this depends on
  type: 'header' | 'paragraph' | 'code' | 'table' | 'list';
}

class SemanticChunkingStrategy {
  createChunks(content: string, options: ChunkingOptions): DocumentChunk[] {
    const lines = content.split('\n');
    const chunks: DocumentChunk[] = [];
    
    let currentChunk: string[] = [];
    let currentType: DocumentChunk['type'] = 'paragraph';
    let currentPriority = 5;
    let chunkStartLine = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineType = this.detectLineType(line);
      
      // Check if we need to start a new chunk
      const shouldSplit = this.shouldCreateNewChunk(
        currentChunk,
        line,
        lineType,
        currentType,
        options
      );
      
      if (shouldSplit && currentChunk.length > 0) {
        chunks.push(this.createChunk(
          currentChunk,
          chunkStartLine,
          i - 1,
          currentType,
          currentPriority
        ));
        
        currentChunk = [];
        chunkStartLine = i;
        currentType = lineType;
        currentPriority = this.calculatePriority(lineType);
      }
      
      currentChunk.push(line);
      
      // Update type if more important content is found
      if (this.calculatePriority(lineType) > currentPriority) {
        currentType = lineType;
        currentPriority = this.calculatePriority(lineType);
      }
    }
    
    // Add final chunk
    if (currentChunk.length > 0) {
      chunks.push(this.createChunk(
        currentChunk,
        chunkStartLine,
        lines.length - 1,
        currentType,
        currentPriority
      ));
    }
    
    return this.optimizeChunks(chunks);
  }
  
  private detectLineType(line: string): DocumentChunk['type'] {
    if (/^#{1,6}\s/.test(line)) return 'header';
    if (/^\s*```/.test(line)) return 'code';
    if (/^\s*\|/.test(line)) return 'table';
    if (/^\s*[-*+]\s/.test(line) || /^\s*\d+\.\s/.test(line)) return 'list';
    return 'paragraph';
  }
  
  private calculatePriority(type: DocumentChunk['type']): number {
    const priorities = {
      'header': 9,
      'code': 7,
      'table': 6,
      'list': 5,
      'paragraph': 4
    };
    return priorities[type];
  }
  
  private shouldCreateNewChunk(
    currentChunk: string[],
    newLine: string,
    newLineType: DocumentChunk['type'],
    currentType: DocumentChunk['type'],
    options: ChunkingOptions
  ): boolean {
    const currentSize = currentChunk.join('\n').length;
    
    // Force split if chunk is too large
    if (currentSize > options.maxChunkSize) {
      return true;
    }
    
    // Don't split if chunk is too small
    if (currentSize < options.minChunkSize) {
      return false;
    }
    
    // Split on headers if prioritized
    if (options.prioritizeHeaders && newLineType === 'header') {
      return true;
    }
    
    // Split on major content type changes
    if (options.preserveStructure) {
      const majorTypeChange = (
        (currentType === 'code' && newLineType !== 'code') ||
        (currentType === 'table' && newLineType !== 'table') ||
        (newLineType === 'code' && currentType !== 'code') ||
        (newLineType === 'table' && currentType !== 'table')
      );
      
      if (majorTypeChange) {
        return true;
      }
    }
    
    return false;
  }
}
```

### Progressive Loading System
```typescript
// Progressive Loading with Visual Feedback
interface LoadingProgress {
  phase: 'parsing' | 'rendering' | 'complete';
  progress: number; // 0-1
  chunksComplete: number;
  totalChunks: number;
  currentChunk?: string;
  estimatedTimeRemaining?: number;
}

class ProgressiveLoader {
  private loadingCallbacks: ((progress: LoadingProgress) => void)[] = [];
  private cancelToken: { cancelled: boolean } = { cancelled: false };
  
  async loadLargeDocument(
    content: string,
    container: HTMLElement,
    onProgress?: (progress: LoadingProgress) => void
  ): Promise<void> {
    if (onProgress) {
      this.loadingCallbacks.push(onProgress);
    }
    
    this.cancelToken.cancelled = false;
    
    try {
      // Phase 1: Parsing
      this.updateProgress({
        phase: 'parsing',
        progress: 0,
        chunksComplete: 0,
        totalChunks: 0
      });
      
      const chunks = await this.parseIntoChunks(content);
      
      if (this.cancelToken.cancelled) return;
      
      // Phase 2: Progressive Rendering
      this.updateProgress({
        phase: 'rendering',
        progress: 0,
        chunksComplete: 0,
        totalChunks: chunks.length
      });
      
      await this.renderProgressively(chunks, container);
      
      // Phase 3: Complete
      this.updateProgress({
        phase: 'complete',
        progress: 1,
        chunksComplete: chunks.length,
        totalChunks: chunks.length
      });
      
    } catch (error) {
      console.error('Progressive loading failed:', error);
      throw error;
    } finally {
      this.loadingCallbacks = [];
    }
  }
  
  private async renderProgressively(
    chunks: DocumentChunk[],
    container: HTMLElement
  ): Promise<void> {
    // Sort chunks by priority for optimal user experience
    const prioritizedChunks = chunks.sort((a, b) => b.priority - a.priority);
    
    const startTime = performance.now();
    
    for (let i = 0; i < prioritizedChunks.length; i++) {
      if (this.cancelToken.cancelled) break;
      
      const chunk = prioritizedChunks[i];
      
      // Render chunk
      const chunkElement = await this.renderChunk(chunk);
      this.insertChunkInOrder(container, chunkElement, chunk);
      
      // Update progress
      const progress = (i + 1) / prioritizedChunks.length;
      const elapsed = performance.now() - startTime;
      const estimatedTotal = elapsed / progress;
      const remaining = estimatedTotal - elapsed;
      
      this.updateProgress({
        phase: 'rendering',
        progress,
        chunksComplete: i + 1,
        totalChunks: prioritizedChunks.length,
        currentChunk: chunk.id,
        estimatedTimeRemaining: remaining
      });
      
      // Yield to browser for responsiveness
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }
  
  private async renderChunk(chunk: DocumentChunk): Promise<HTMLElement> {
    // Use worker for large chunks
    if (chunk.content.length > 10000) {
      const workerPool = WorkerPool.getInstance();
      const html = await workerPool.processLargeDocument(chunk.content, {
        format: 'html',
        preserveStructure: true
      });
      
      const element = document.createElement('div');
      element.innerHTML = html;
      element.setAttribute('data-chunk-id', chunk.id);
      element.setAttribute('data-chunk-priority', chunk.priority.toString());
      
      return element;
    }
    
    // Process on main thread for small chunks
    const html = marked(chunk.content, { gfm: true });
    const element = document.createElement('div');
    element.innerHTML = html;
    element.setAttribute('data-chunk-id', chunk.id);
    
    return element;
  }
  
  cancel(): void {
    this.cancelToken.cancelled = true;
  }
}
```

### Virtualized Preview Component
```typescript
// Enhanced Virtual Scrolling for Large Documents
interface VirtualizedPreviewProps {
  content: string;
  chunkSize?: number;
  viewportHeight: number;
  onProgress?: (progress: LoadingProgress) => void;
}

const VirtualizedPreview: React.FC<VirtualizedPreviewProps> = ({
  content,
  chunkSize = 20000,
  viewportHeight,
  onProgress
}) => {
  const [chunks, setChunks] = useState<DocumentChunk[]>([]);
  const [renderedChunks, setRenderedChunks] = useState<Map<string, string>>(new Map());
  const [visibleRange, setVisibleRange] = useState<{start: number; end: number}>({start: 0, end: 5});
  const [scrollTop, setScrollTop] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  
  useEffect(() => {
    const loadContent = async () => {
      setIsLoading(true);
      
      try {
        const progressiveLoader = new ProgressiveLoader();
        const documentChunks = await progressiveLoader.parseIntoChunks(content);
        setChunks(documentChunks);
        
        // Pre-render visible chunks
        await renderVisibleChunks(documentChunks.slice(0, 5));
      } catch (error) {
        console.error('Failed to load content:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (content) {
      loadContent();
    }
  }, [content]);
  
  useEffect(() => {
    // Set up intersection observer for lazy loading
    if (observerRef.current) {
      observerRef.current.disconnect();
    }
    
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const chunkId = entry.target.getAttribute('data-chunk-id');
            if (chunkId && !renderedChunks.has(chunkId)) {
              renderChunkById(chunkId);
            }
          }
        });
      },
      {
        rootMargin: '100px', // Load content 100px before it becomes visible
        threshold: 0.1
      }
    );
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [chunks]);
  
  const renderVisibleChunks = async (visibleChunks: DocumentChunk[]) => {
    const workerPool = WorkerPool.getInstance();
    const newRenderedChunks = new Map(renderedChunks);
    
    for (const chunk of visibleChunks) {
      if (!newRenderedChunks.has(chunk.id)) {
        try {
          const html = await workerPool.processLargeDocument(chunk.content, {
            format: 'html',
            preserveStructure: true
          });
          newRenderedChunks.set(chunk.id, html);
        } catch (error) {
          console.warn(`Failed to render chunk ${chunk.id}:`, error);
          // Fallback to main thread
          const html = marked(chunk.content, { gfm: true });
          newRenderedChunks.set(chunk.id, html);
        }
      }
    }
    
    setRenderedChunks(newRenderedChunks);
  };
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    setScrollTop(scrollTop);
    
    // Calculate visible range based on scroll position
    const chunkHeight = 200; // Estimated average chunk height
    const startIndex = Math.floor(scrollTop / chunkHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(viewportHeight / chunkHeight) + 2,
      chunks.length - 1
    );
    
    setVisibleRange({ start: startIndex, end: endIndex });
    
    // Render chunks that are coming into view
    const visibleChunks = chunks.slice(startIndex, endIndex + 1);
    renderVisibleChunks(visibleChunks);
  }, [chunks, viewportHeight]);
  
  if (isLoading) {
    return (
      <div className="virtualized-preview-loading">
        <div className="loading-indicator">
          <div className="spinner"></div>
          <p>Loading large document...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div
      ref={containerRef}
      className="virtualized-preview"
      style={{ height: viewportHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      {chunks.map((chunk, index) => (
        <VirtualizedChunk
          key={chunk.id}
          chunk={chunk}
          isVisible={index >= visibleRange.start && index <= visibleRange.end}
          html={renderedChunks.get(chunk.id)}
          onIntersection={(id) => {
            if (observerRef.current) {
              const element = document.querySelector(`[data-chunk-id="${id}"]`);
              if (element) {
                observerRef.current.observe(element);
              }
            }
          }}
        />
      ))}
    </div>
  );
};
```

### Performance Monitoring and Device Detection
```typescript
// Advanced Performance Monitoring
class DevicePerformanceDetector {
  private performanceProfile: 'high' | 'medium' | 'low' = 'medium';
  private benchmarkResults: BenchmarkResult[] = [];
  
  async detectPerformanceProfile(): Promise<'high' | 'medium' | 'low'> {
    const benchmarks = await this.runPerformanceBenchmarks();
    
    let score = 0;
    
    // CPU performance (parsing speed)
    if (benchmarks.parsingSpeed > 1000000) score += 3; // 1MB/s
    else if (benchmarks.parsingSpeed > 500000) score += 2; // 500KB/s
    else score += 1;
    
    // Memory capacity
    if (benchmarks.memoryLimit > 2000000000) score += 3; // 2GB
    else if (benchmarks.memoryLimit > 1000000000) score += 2; // 1GB
    else score += 1;
    
    // Rendering performance
    if (benchmarks.renderingFPS > 45) score += 3;
    else if (benchmarks.renderingFPS > 30) score += 2;
    else score += 1;
    
    // Hardware concurrency
    if (navigator.hardwareConcurrency >= 8) score += 2;
    else if (navigator.hardwareConcurrency >= 4) score += 1;
    
    // Determine profile based on score
    if (score >= 10) this.performanceProfile = 'high';
    else if (score >= 6) this.performanceProfile = 'medium';
    else this.performanceProfile = 'low';
    
    return this.performanceProfile;
  }
  
  private async runPerformanceBenchmarks(): Promise<BenchmarkResult> {
    // Parsing speed benchmark
    const parsingStart = performance.now();
    const testContent = 'x'.repeat(100000); // 100KB test
    marked(testContent);
    const parsingTime = performance.now() - parsingStart;
    const parsingSpeed = testContent.length / (parsingTime / 1000); // bytes per second
    
    // Memory benchmark
    let memoryLimit = 0;
    if ('memory' in performance) {
      memoryLimit = (performance as any).memory.jsHeapSizeLimit;
    }
    
    // Rendering benchmark
    const renderingFPS = await this.measureRenderingFPS();
    
    return {
      parsingSpeed,
      memoryLimit,
      renderingFPS,
      hardwareConcurrency: navigator.hardwareConcurrency || 1,
      userAgent: navigator.userAgent
    };
  }
  
  getOptimalSettings(): ProcessingSettings {
    switch (this.performanceProfile) {
      case 'high':
        return {
          workerPoolSize: Math.min(8, navigator.hardwareConcurrency || 4),
          chunkSize: 50000,
          preloadChunks: 10,
          enableVirtualScrolling: true,
          maxCacheSize: 200
        };
      case 'medium':
        return {
          workerPoolSize: Math.min(4, navigator.hardwareConcurrency || 2),
          chunkSize: 25000,
          preloadChunks: 5,
          enableVirtualScrolling: true,
          maxCacheSize: 100
        };
      case 'low':
        return {
          workerPoolSize: 1,
          chunkSize: 10000,
          preloadChunks: 2,
          enableVirtualScrolling: true,
          maxCacheSize: 50
        };
    }
  }
}
```

## Testing
### Testing Standards
- **Performance Tests:** Benchmark 200KB file handling across all browsers
- **Memory Tests:** Monitor memory usage during large file operations
- **Worker Tests:** Test Web Worker functionality and error recovery
- **Stress Tests:** Test with files at the 200KB limit

### Key Test Scenarios
1. Smooth operation with 200KB markdown files
2. Web Worker processing for files ≥100KB
3. Progressive loading with visual feedback
4. Memory stability during large file editing
5. Performance monitoring and device detection
6. Lazy loading and virtual scrolling efficiency
7. Error recovery and fallback mechanisms
8. Cross-browser performance consistency

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*