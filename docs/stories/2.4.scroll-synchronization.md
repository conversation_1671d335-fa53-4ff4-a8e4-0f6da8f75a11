# <!-- Powered by BMAD™ Core -->

# Story 2.4: Scroll Synchronization Between Editor and Preview

## Status
Draft

## Story
**As a** user,
**I want** the editor and preview to scroll together,
**so that** I can see the rendered version of the content I'm currently editing.

## Acceptance Criteria
1. Scrolling in editor automatically scrolls preview to corresponding content
2. Scrolling in preview automatically scrolls editor to corresponding line
3. Synchronization maintains approximate accuracy for complex markdown structures
4. Performance remains smooth during scroll synchronization with large documents
5. Scroll synchronization can be toggled on/off via user preference
6. Edge cases handled gracefully: tables, code blocks, nested lists
7. Synchronization accuracy within 5% of document position for most content types
8. Smooth scrolling animations maintain visual connection between panes

## Tasks / Subtasks

- [ ] Create scroll synchronization manager (AC: 1, 2, 4)
  - [ ] Implement ScrollSyncManager service in src/services/
  - [ ] Create bidirectional scroll event handling system
  - [ ] Implement performance optimization for smooth scrolling
  - [ ] Add scroll position mapping between editor lines and preview elements

- [ ] Implement line-to-element mapping system (AC: 3, 6, 7)
  - [ ] Create mapping between editor lines and preview DOM elements
  - [ ] Handle complex structures: tables, code blocks, lists
  - [ ] Implement fallback strategies for unmappable content
  - [ ] Maintain accuracy within 5% tolerance for most content

- [ ] Add user preference controls (AC: 5)
  - [ ] Create toggle for enabling/disabling scroll synchronization
  - [ ] Store preference in localStorage for persistence
  - [ ] Add UI control in settings or header
  - [ ] Provide smooth transition when toggling sync

- [ ] Optimize for smooth animations (AC: 8)
  - [ ] Implement smooth scrolling with CSS transitions
  - [ ] Add scroll momentum preservation
  - [ ] Prevent scroll fighting between panes
  - [ ] Create visual indicators for sync status

- [ ] Handle edge cases and complex structures (AC: 6)
  - [ ] Test synchronization with markdown tables
  - [ ] Handle fenced code blocks and syntax highlighting
  - [ ] Manage nested list structures
  - [ ] Test with mixed content and complex formatting

- [ ] Create performance monitoring (AC: 4)
  - [ ] Monitor scroll synchronization performance
  - [ ] Implement throttling for high-frequency scroll events
  - [ ] Add performance degradation detection
  - [ ] Create fallback modes for slower devices

- [ ] Integrate with CodeMirror and preview components (AC: 1, 2)
  - [ ] Connect to CodeMirror scroll events
  - [ ] Integrate with PreviewPane scroll handling
  - [ ] Ensure compatibility with real-time preview updates
  - [ ] Handle scroll events during content changes

## Dev Notes

### Previous Story Insights
Story 2.3 implemented GitHub Flavored Markdown rendering. This story adds scroll synchronization to create a cohesive editing experience where users can easily correlate their editing position with the preview output.

### Scroll Synchronization Architecture
[Source: architecture/scroll-synchronization-technical-risk-mitigation.md]
```typescript
// Line-to-Element Mapping System
interface LineMapping {
  editorLine: number;
  previewElement: HTMLElement;
  elementOffset: number;
  confidence: number; // 0-1, mapping confidence
}

class ScrollSyncManager {
  private lineMapping: LineMapping[] = [];
  private isUpdatingScroll = false;
  private throttleTimeout: number | null = null;
  
  constructor(
    private editor: EditorView,
    private preview: HTMLElement,
    private preferences: ScrollSyncPreferences
  ) {
    this.setupScrollListeners();
  }
  
  private setupScrollListeners() {
    // Editor scroll events
    this.editor.scrollDOM.addEventListener('scroll', 
      this.throttle(() => this.handleEditorScroll(), 16) // 60fps
    );
    
    // Preview scroll events
    this.preview.addEventListener('scroll',
      this.throttle(() => this.handlePreviewScroll(), 16)
    );
  }
  
  private handleEditorScroll() {
    if (this.isUpdatingScroll || !this.preferences.enabled) return;
    
    const editorScrollTop = this.editor.scrollDOM.scrollTop;
    const currentLine = this.getLineFromScrollPosition(editorScrollTop);
    const mapping = this.findClosestMapping(currentLine);
    
    if (mapping && mapping.confidence > 0.7) {
      this.syncPreviewToLine(mapping);
    }
  }
  
  private syncPreviewToLine(mapping: LineMapping) {
    this.isUpdatingScroll = true;
    
    const targetScrollTop = mapping.previewElement.offsetTop + mapping.elementOffset;
    
    if (this.preferences.smoothScrolling) {
      this.smoothScrollTo(this.preview, targetScrollTop);
    } else {
      this.preview.scrollTop = targetScrollTop;
    }
    
    setTimeout(() => {
      this.isUpdatingScroll = false;
    }, 100);
  }
}
```

### Line-to-Element Mapping Implementation
[Source: architecture/scroll-synchronization-technical-risk-mitigation.md]
```typescript
// Line-to-Element Mapping System
class LineElementMapper {
  private markdownAst: any;
  private previewElements: HTMLElement[];
  
  buildMapping(content: string, previewElement: HTMLElement): LineMapping[] {
    const mappings: LineMapping[] = [];
    const lines = content.split('\n');
    const walker = document.createTreeWalker(
      previewElement,
      NodeFilter.SHOW_ELEMENT,
      null,
      false
    );
    
    let currentLine = 0;
    let element: HTMLElement | null;
    
    while (element = walker.nextNode() as HTMLElement) {
      if (this.isContentElement(element)) {
        const lineInfo = this.findSourceLine(element, lines, currentLine);
        if (lineInfo) {
          mappings.push({
            editorLine: lineInfo.line,
            previewElement: element,
            elementOffset: 0,
            confidence: lineInfo.confidence
          });
          currentLine = lineInfo.line;
        }
      }
    }
    
    return this.optimizeMappings(mappings);
  }
  
  private findSourceLine(element: HTMLElement, lines: string[], startLine: number): 
    { line: number; confidence: number } | null {
    
    const text = element.textContent?.trim();
    if (!text) return null;
    
    // Look for exact matches first
    for (let i = startLine; i < lines.length; i++) {
      if (lines[i].includes(text.substring(0, 50))) {
        return { line: i, confidence: 1.0 };
      }
    }
    
    // Look for partial matches with lower confidence
    for (let i = startLine; i < Math.min(lines.length, startLine + 10); i++) {
      const similarity = this.calculateSimilarity(text, lines[i]);
      if (similarity > 0.6) {
        return { line: i, confidence: similarity };
      }
    }
    
    return null;
  }
  
  private calculateSimilarity(text1: string, text2: string): number {
    // Simple similarity calculation - could be enhanced
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    const commonWords = words1.filter(word => words2.includes(word));
    
    return commonWords.length / Math.max(words1.length, words2.length);
  }
}
```

### Smooth Scrolling Implementation
```typescript
// Smooth Scrolling with Animation
class SmoothScroller {
  private animationId: number | null = null;
  
  scrollTo(element: HTMLElement, targetScrollTop: number, duration = 300) {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    const start = element.scrollTop;
    const distance = targetScrollTop - start;
    const startTime = performance.now();
    
    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function for smooth animation
      const easeInOutQuad = (t: number) => 
        t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      
      const currentPosition = start + distance * easeInOutQuad(progress);
      element.scrollTop = currentPosition;
      
      if (progress < 1) {
        this.animationId = requestAnimationFrame(animateScroll);
      } else {
        this.animationId = null;
      }
    };
    
    this.animationId = requestAnimationFrame(animateScroll);
  }
  
  cancel() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }
}
```

### Performance Optimization
```typescript
// Performance Monitoring and Optimization
class ScrollPerformanceMonitor {
  private lastScrollTime = 0;
  private scrollEventCount = 0;
  private performanceMode: 'high' | 'balanced' | 'low' = 'balanced';
  
  checkPerformance(): void {
    const now = performance.now();
    this.scrollEventCount++;
    
    if (now - this.lastScrollTime > 1000) {
      const eventsPerSecond = this.scrollEventCount;
      
      if (eventsPerSecond > 30 && this.performanceMode !== 'low') {
        console.warn('High scroll frequency detected, reducing sync quality');
        this.recommendPerformanceMode('low');
      } else if (eventsPerSecond < 15 && this.performanceMode !== 'high') {
        this.recommendPerformanceMode('high');
      }
      
      this.scrollEventCount = 0;
      this.lastScrollTime = now;
    }
  }
  
  private recommendPerformanceMode(mode: 'high' | 'balanced' | 'low') {
    this.performanceMode = mode;
    
    // Adjust throttling based on performance
    const throttleMs = {
      'high': 8,    // 120fps
      'balanced': 16, // 60fps
      'low': 32     // 30fps
    }[mode];
    
    // Could emit event to update scroll sync settings
    window.dispatchEvent(new CustomEvent('scroll-performance-change', {
      detail: { mode, throttleMs }
    }));
  }
}
```

### Edge Case Handling
```typescript
// Edge Case Handling for Complex Structures
class EdgeCaseHandler {
  handleComplexStructures(element: HTMLElement, mapping: LineMapping): LineMapping {
    // Handle tables
    if (element.tagName === 'TABLE') {
      return this.handleTableMapping(element, mapping);
    }
    
    // Handle code blocks
    if (element.classList.contains('hljs') || element.tagName === 'PRE') {
      return this.handleCodeBlockMapping(element, mapping);
    }
    
    // Handle nested lists
    if (element.tagName === 'UL' || element.tagName === 'OL') {
      return this.handleListMapping(element, mapping);
    }
    
    return mapping;
  }
  
  private handleTableMapping(table: HTMLElement, mapping: LineMapping): LineMapping {
    // Tables often span multiple lines, adjust confidence and offset
    const rows = table.querySelectorAll('tr');
    const estimatedLines = rows.length + 2; // Header separator + content
    
    return {
      ...mapping,
      confidence: Math.max(0.5, mapping.confidence - 0.2),
      elementOffset: table.offsetHeight / estimatedLines
    };
  }
  
  private handleCodeBlockMapping(codeBlock: HTMLElement, mapping: LineMapping): LineMapping {
    // Code blocks have predictable line-to-element mapping
    const lines = codeBlock.textContent?.split('\n').length || 1;
    const lineHeight = codeBlock.offsetHeight / lines;
    
    return {
      ...mapping,
      confidence: 0.95, // High confidence for code blocks
      elementOffset: lineHeight / 2
    };
  }
  
  private handleListMapping(list: HTMLElement, mapping: LineMapping): LineMapping {
    // Lists can have complex nesting, reduce confidence slightly
    return {
      ...mapping,
      confidence: Math.max(0.6, mapping.confidence - 0.1)
    };
  }
}
```

### User Preferences Interface
```typescript
// Scroll Synchronization Preferences
interface ScrollSyncPreferences {
  enabled: boolean;
  smoothScrolling: boolean;
  syncAccuracy: 'high' | 'balanced' | 'fast';
  throttleDelay: number;
}

const useScrollSyncPreferences = () => {
  const [preferences, setPreferences] = useState<ScrollSyncPreferences>({
    enabled: true,
    smoothScrolling: true,
    syncAccuracy: 'balanced',
    throttleDelay: 16
  });

  const updatePreference = <K extends keyof ScrollSyncPreferences>(
    key: K,
    value: ScrollSyncPreferences[K]
  ) => {
    const updated = { ...preferences, [key]: value };
    setPreferences(updated);
    localStorage.setItem('scroll-sync-preferences', JSON.stringify(updated));
  };

  return { preferences, updatePreference };
};

// UI Component for Scroll Sync Settings
const ScrollSyncSettings = () => {
  const { preferences, updatePreference } = useScrollSyncPreferences();
  
  return (
    <div className="scroll-sync-settings">
      <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={preferences.enabled}
          onChange={(e) => updatePreference('enabled', e.target.checked)}
        />
        <span>Enable scroll synchronization</span>
      </label>
      
      <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={preferences.smoothScrolling}
          onChange={(e) => updatePreference('smoothScrolling', e.target.checked)}
          disabled={!preferences.enabled}
        />
        <span>Smooth scrolling animations</span>
      </label>
      
      <div className="mt-4">
        <label className="block text-sm font-medium">Sync Accuracy:</label>
        <select
          value={preferences.syncAccuracy}
          onChange={(e) => updatePreference('syncAccuracy', e.target.value as any)}
          disabled={!preferences.enabled}
          className="mt-1 block w-full rounded-md border-gray-300"
        >
          <option value="fast">Fast (lower accuracy)</option>
          <option value="balanced">Balanced</option>
          <option value="high">High accuracy (slower)</option>
        </select>
      </div>
    </div>
  );
};
```

### Integration with Real-Time Preview
```typescript
// Integration with Real-Time Preview Updates
class ScrollSyncIntegration {
  constructor(
    private scrollSyncManager: ScrollSyncManager,
    private previewSyncManager: PreviewSyncManager
  ) {
    this.setupIntegration();
  }
  
  private setupIntegration() {
    // Update line mappings when content changes
    this.previewSyncManager.onPreviewUpdate((content, html) => {
      // Rebuild line mappings after content update
      setTimeout(() => {
        this.scrollSyncManager.rebuildMapping(content);
      }, 100); // Allow DOM to update
    });
    
    // Preserve scroll position during preview updates
    this.previewSyncManager.onBeforeUpdate(() => {
      const currentPosition = this.scrollSyncManager.getCurrentSyncPosition();
      this.scrollSyncManager.preservePosition(currentPosition);
    });
  }
}
```

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Unit Tests:** Test line-to-element mapping and scroll calculation algorithms
- **Integration Tests:** Test scroll synchronization with real-time preview updates
- **Performance Tests:** Verify smooth scrolling with large documents
- **E2E Tests:** Test user interactions with scroll synchronization enabled/disabled

### Key Test Scenarios
1. Bidirectional scroll synchronization accuracy
2. Performance with documents up to 200KB
3. Handling of complex markdown structures (tables, code, lists)
4. Smooth scrolling animations and visual feedback
5. User preference toggling and persistence
6. Edge case handling with malformed content
7. Integration with real-time preview updates
8. Memory usage during extended scrolling sessions

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*