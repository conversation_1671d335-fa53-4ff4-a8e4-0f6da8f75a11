# <!-- Powered by BMAD™ Core -->

# Story 2.5: Real-Time Preview Performance Optimization

## Status
Draft

## Story
**As a** user,
**I want** smooth, responsive editing even with complex markdown documents,
**so that** the tool doesn't slow down my writing workflow.

## Acceptance Criteria
1. Preview updates maintain sub-100ms response time for documents up to 50KB
2. Documents 50KB-200KB use Web Worker parsing to maintain UI responsiveness
3. Virtual scrolling implemented for previews of very long documents
4. Memory usage optimization prevents performance degradation during long editing sessions
5. Rendering performance monitoring with automatic degradation for slower devices
6. Caching strategies minimize redundant parsing for unchanged document sections
7. Progressive rendering shows partial results for extremely large documents
8. Performance metrics displayed in developer tools for monitoring and debugging

## Tasks / Subtasks

- [ ] Implement Web Worker parsing for large documents (AC: 2)
  - [ ] Create markdown-worker.ts in src/workers/
  - [ ] Implement WorkerManager service for background processing
  - [ ] Configure automatic worker usage for documents ≥50KB
  - [ ] Handle worker communication and error recovery

- [ ] Create performance monitoring system (AC: 1, 5, 8)
  - [ ] Implement PerformanceTracker utility in src/utils/performance.ts
  - [ ] Monitor parsing times and memory usage
  - [ ] Create automatic performance degradation system
  - [ ] Add developer tools integration for metrics

- [ ] Implement intelligent caching system (AC: 6)
  - [ ] Create ContentCache service for parsed results
  - [ ] Implement differential content analysis
  - [ ] Cache stable document sections
  - [ ] Add cache invalidation strategies

- [ ] Add virtual scrolling for long documents (AC: 3)
  - [ ] Implement VirtualScrollPreview component
  - [ ] Create viewport-based rendering system
  - [ ] Handle dynamic content height calculations
  - [ ] Maintain scroll position during virtualization

- [ ] Create progressive rendering system (AC: 7)
  - [ ] Implement chunked document processing
  - [ ] Show progressive results during parsing
  - [ ] Add loading indicators for long operations
  - [ ] Handle user interactions during progressive loading

- [ ] Optimize memory usage (AC: 4)
  - [ ] Implement memory monitoring and cleanup
  - [ ] Optimize DOM node management
  - [ ] Add garbage collection hints
  - [ ] Monitor memory leaks during long sessions

- [ ] Create adaptive performance modes (AC: 5)
  - [ ] Detect device performance capabilities
  - [ ] Implement automatic quality adjustment
  - [ ] Create user-selectable performance modes
  - [ ] Add performance recommendations

## Dev Notes

### Previous Story Insights
Story 2.4 implemented scroll synchronization between editor and preview. This story optimizes the real-time preview performance to handle large documents smoothly while maintaining the responsive editing experience.

### Web Worker Implementation Architecture
[Source: architecture/epic-based-architecture-evolution.md]
```typescript
// Web Worker Implementation for Large Files
// markdown-worker.ts
import { marked } from 'marked';
import { gfm } from 'marked-gfm';

// Configure marked for GFM compatibility
marked.use(gfm());

self.onmessage = (event: MessageEvent<WorkerMessage>) => {
  const { type, id, payload } = event.data;
  
  if (type === 'parse') {
    try {
      // Process large content in chunks for progress reporting
      const chunks = chunkContent(payload.content, 10000); // 10KB chunks
      let html = '';
      
      chunks.forEach((chunk, index) => {
        html += marked(chunk, payload.options);
        
        // Report progress for very large files
        if (chunks.length > 5) {
          self.postMessage({
            type: 'progress',
            id,
            payload: { progress: (index + 1) / chunks.length }
          });
        }
      });
      
      self.postMessage({
        type: 'parsed',
        id,
        payload: { html }
      });
    } catch (error) {
      self.postMessage({
        type: 'error',
        id,
        payload: { error: error.message }
      });
    }
  }
};

function chunkContent(content: string, chunkSize: number): string[] {
  const chunks = [];
  for (let i = 0; i < content.length; i += chunkSize) {
    chunks.push(content.slice(i, i + chunkSize));
  }
  return chunks;
}
```

### Worker Manager Service
```typescript
// WorkerManager for Background Processing
interface WorkerMessage {
  type: 'parse' | 'cancel';
  id: string;
  payload: {
    content: string;
    options: ParseOptions;
  };
}

interface WorkerResponse {
  type: 'parsed' | 'error' | 'progress';
  id: string;
  payload: {
    html?: string;
    error?: string;
    progress?: number;
  };
}

class WorkerManager {
  private worker: Worker | null = null;
  private pendingRequests = new Map<string, WorkerPromise>();
  private isInitialized = false;
  
  constructor() {
    this.initializeWorker();
  }
  
  async parseMarkdown(content: string, options: ParseOptions): Promise<string> {
    if (!this.worker || !this.isInitialized) {
      throw new Error('Worker not available');
    }
    
    const id = crypto.randomUUID();
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      
      this.worker!.postMessage({
        type: 'parse',
        id,
        payload: { content, options }
      });
      
      // Timeout for very large files (30 seconds)
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('Worker timeout'));
        }
      }, 30000);
    });
  }
  
  private initializeWorker() {
    try {
      this.worker = new Worker(
        new URL('../workers/markdown-worker.ts', import.meta.url),
        { type: 'module' }
      );
      
      this.worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
        const { type, id, payload } = event.data;
        const request = this.pendingRequests.get(id);
        
        if (!request) return;
        
        switch (type) {
          case 'parsed':
            this.pendingRequests.delete(id);
            request.resolve(payload.html!);
            break;
          case 'error':
            this.pendingRequests.delete(id);
            request.reject(new Error(payload.error));
            break;
          case 'progress':
            // Could emit progress events here
            request.onProgress?.(payload.progress!);
            break;
        }
      };
      
      this.worker.onerror = (error) => {
        console.error('Worker error:', error);
        this.handleWorkerError(error);
      };
      
      this.isInitialized = true;
    } catch (error) {
      console.warn('Web Worker not supported, falling back to main thread');
      this.isInitialized = false;
    }
  }
  
  private handleWorkerError(error: ErrorEvent) {
    // Fallback to main thread processing
    this.isInitialized = false;
    this.pendingRequests.forEach((request) => {
      request.reject(new Error('Worker failed, falling back to main thread'));
    });
    this.pendingRequests.clear();
  }
}
```

### Performance Monitoring System
[Source: architecture/performance-and-deployment-strategy.md]
```typescript
// Performance Tracking and Monitoring
interface PerformanceMetrics {
  parseTime: number;
  renderTime: number;
  memoryUsage: number;
  documentSize: number;
  timestamp: number;
}

class PerformanceTracker {
  private static metrics: PerformanceMetrics[] = [];
  private static readonly MAX_METRICS = 1000;
  private static performanceObserver: PerformanceObserver | null = null;
  
  static initialize() {
    // Initialize Performance Observer for detailed metrics
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.name.startsWith('markdown-parse')) {
            this.recordCustomMetric(entry);
          }
        });
      });
      
      this.performanceObserver.observe({ entryTypes: ['measure'] });
    }
  }
  
  static startParsing(documentSize: number): string {
    const id = `parse-${Date.now()}`;
    performance.mark(`markdown-parse-start-${id}`);
    return id;
  }
  
  static endParsing(id: string, documentSize: number) {
    performance.mark(`markdown-parse-end-${id}`);
    performance.measure(
      `markdown-parse-${id}`,
      `markdown-parse-start-${id}`,
      `markdown-parse-end-${id}`
    );
    
    const parseTime = performance.getEntriesByName(`markdown-parse-${id}`)[0]?.duration || 0;
    const memoryUsage = this.getMemoryUsage();
    
    const metric: PerformanceMetrics = {
      parseTime,
      renderTime: 0, // Will be updated by render tracking
      memoryUsage,
      documentSize,
      timestamp: Date.now()
    };
    
    this.recordMetric(metric);
    this.analyzePerformance();
  }
  
  private static getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
  
  private static recordMetric(metric: PerformanceMetrics) {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
    
    // Send to analytics if configured
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'preview_performance', {
        custom_map: { 
          metric1: 'parse_time',
          metric2: 'document_size',
          metric3: 'memory_usage'
        },
        metric1: metric.parseTime,
        metric2: metric.documentSize,
        metric3: metric.memoryUsage
      });
    }
  }
  
  private static analyzePerformance() {
    const recentMetrics = this.metrics.slice(-10);
    const avgParseTime = recentMetrics.reduce((sum, m) => sum + m.parseTime, 0) / recentMetrics.length;
    
    // Recommend performance adjustments
    if (avgParseTime > 200) {
      this.recommendPerformanceMode('fast');
    } else if (avgParseTime > 100) {
      this.recommendPerformanceMode('balanced');
    }
    
    // Memory leak detection
    const memoryTrend = this.analyzeMemoryTrend(recentMetrics);
    if (memoryTrend > 1.5) { // 50% increase
      console.warn('Potential memory leak detected');
      this.triggerMemoryCleanup();
    }
  }
  
  static getPerformanceReport(): any {
    const recentMetrics = this.metrics.slice(-100);
    return {
      averageParseTime: recentMetrics.reduce((sum, m) => sum + m.parseTime, 0) / recentMetrics.length,
      peakMemoryUsage: Math.max(...recentMetrics.map(m => m.memoryUsage)),
      documentSizeDistribution: this.getDocumentSizeDistribution(recentMetrics),
      performanceOverTime: recentMetrics.map(m => ({
        timestamp: m.timestamp,
        parseTime: m.parseTime,
        memoryUsage: m.memoryUsage
      }))
    };
  }
}
```

### Intelligent Caching System
```typescript
// Content Caching for Performance Optimization
interface CacheEntry {
  content: string;
  html: string;
  timestamp: number;
  accessCount: number;
  documentSize: number;
}

class ContentCache {
  private static cache = new Map<string, CacheEntry>();
  private static readonly MAX_CACHE_SIZE = 100;
  private static readonly CACHE_TTL = 300000; // 5 minutes
  
  static get(content: string): string | null {
    const key = this.generateCacheKey(content);
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if entry is still valid
    if (Date.now() - entry.timestamp > this.CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.timestamp = Date.now();
    
    return entry.html;
  }
  
  static set(content: string, html: string): void {
    const key = this.generateCacheKey(content);
    const entry: CacheEntry = {
      content: content.substring(0, 1000), // Store prefix for debugging
      html,
      timestamp: Date.now(),
      accessCount: 1,
      documentSize: content.length
    };
    
    // Cleanup old entries if cache is full
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntries();
    }
    
    this.cache.set(key, entry);
  }
  
  private static generateCacheKey(content: string): string {
    // Use a simple hash for caching (could be improved with proper hashing)
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }
  
  private static evictOldestEntries(): void {
    // Sort by last access time and remove oldest 20%
    const entries = Array.from(this.cache.entries());
    entries.sort(([, a], [, b]) => a.timestamp - b.timestamp);
    
    const toRemove = Math.floor(entries.length * 0.2);
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0]);
    }
  }
  
  static getStats(): any {
    const entries = Array.from(this.cache.values());
    return {
      cacheSize: this.cache.size,
      totalMemoryUsage: entries.reduce((sum, entry) => sum + entry.html.length, 0),
      averageDocumentSize: entries.reduce((sum, entry) => sum + entry.documentSize, 0) / entries.length,
      hitRate: this.calculateHitRate(),
      oldestEntry: Math.min(...entries.map(e => e.timestamp)),
      mostAccessed: Math.max(...entries.map(e => e.accessCount))
    };
  }
}
```

### Virtual Scrolling Implementation
```typescript
// Virtual Scrolling for Large Documents
interface VirtualScrollProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
}

const VirtualScrollPreview: React.FC<VirtualScrollProps> = ({
  items,
  itemHeight,
  containerHeight,
  renderItem
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleStartIndex = Math.floor(scrollTop / itemHeight);
  const visibleEndIndex = Math.min(
    visibleStartIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length - 1
  );
  
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleStartIndex * itemHeight;
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);
  
  return (
    <div
      className="virtual-scroll-container"
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {items.slice(visibleStartIndex, visibleEndIndex + 1).map((item, index) =>
            renderItem(item, visibleStartIndex + index)
          )}
        </div>
      </div>
    </div>
  );
};
```

### Progressive Rendering System
```typescript
// Progressive Rendering for Large Documents
class ProgressiveRenderer {
  private renderQueue: RenderChunk[] = [];
  private isRendering = false;
  private onProgressCallback?: (progress: number) => void;
  
  async renderProgressively(
    content: string,
    container: HTMLElement,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    this.onProgressCallback = onProgress;
    this.renderQueue = this.createRenderChunks(content);
    
    // Show initial loading state
    container.innerHTML = '<div class="progressive-loading">Rendering document...</div>';
    
    await this.processRenderQueue(container);
  }
  
  private createRenderChunks(content: string): RenderChunk[] {
    const chunks: RenderChunk[] = [];
    const lines = content.split('\n');
    const chunkSize = 50; // Lines per chunk
    
    for (let i = 0; i < lines.length; i += chunkSize) {
      const chunkLines = lines.slice(i, i + chunkSize);
      chunks.push({
        id: `chunk-${i}`,
        content: chunkLines.join('\n'),
        startLine: i,
        endLine: Math.min(i + chunkSize - 1, lines.length - 1)
      });
    }
    
    return chunks;
  }
  
  private async processRenderQueue(container: HTMLElement): Promise<void> {
    this.isRendering = true;
    const resultContainer = document.createElement('div');
    container.appendChild(resultContainer);
    
    for (let i = 0; i < this.renderQueue.length; i++) {
      const chunk = this.renderQueue[i];
      
      // Render chunk
      const html = await this.renderChunk(chunk.content);
      const chunkElement = document.createElement('div');
      chunkElement.innerHTML = html;
      chunkElement.setAttribute('data-chunk-id', chunk.id);
      
      resultContainer.appendChild(chunkElement);
      
      // Report progress
      const progress = (i + 1) / this.renderQueue.length;
      this.onProgressCallback?.(progress);
      
      // Yield to browser for responsiveness
      await new Promise(resolve => setTimeout(resolve, 0));
    }
    
    // Remove loading indicator
    const loadingElement = container.querySelector('.progressive-loading');
    if (loadingElement) {
      loadingElement.remove();
    }
    
    this.isRendering = false;
  }
  
  private async renderChunk(content: string): Promise<string> {
    // Use worker for large chunks
    if (content.length > 10000) {
      const workerManager = new WorkerManager();
      return await workerManager.parseMarkdown(content, { gfm: true });
    }
    
    // Use main thread for small chunks
    return marked(content, { gfm: true });
  }
}
```

## Testing
### Testing Standards
[Source: architecture/tech-stack.md]
- **Performance Tests:** Measure parsing and rendering times with various document sizes
- **Memory Tests:** Monitor memory usage during extended editing sessions
- **Worker Tests:** Test Web Worker functionality and error handling
- **Cache Tests:** Verify caching effectiveness and memory management

### Key Test Scenarios
1. Sub-100ms response time for documents up to 50KB
2. Web Worker parsing for documents 50KB-200KB
3. Virtual scrolling performance with very long documents
4. Memory stability during 30+ minute editing sessions
5. Caching effectiveness for repeated content
6. Progressive rendering with user interaction
7. Performance degradation on slower devices
8. Developer tools metrics accuracy

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*