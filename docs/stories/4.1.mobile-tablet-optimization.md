# <!-- Powered by BMAD™ Core -->

# Story 4.1: Mobile and Tablet Optimization

## Status
Draft

## Story
**As a** mobile user,
**I want** a touch-optimized editing experience that works well on tablets and phones,
**so that** I can edit markdown content efficiently on any device.

## Acceptance Criteria
1. Touch-optimized interface with appropriate touch targets (minimum 44px) and gesture support
2. Tablet interface (768px+) uses tabbed layout switching between editor and preview modes
3. Mobile interface (under 768px) provides stacked layout with swipe navigation between panes
4. Virtual keyboard handling maintains proper viewport and cursor positioning
5. Touch selection tools for text highlighting and manipulation on touchscreen devices
6. Responsive typography and spacing optimized for mobile reading and editing
7. Drag-and-drop file functionality adapted for mobile file browsers and cloud storage
8. Performance optimization ensures smooth scrolling and typing on mobile devices
9. Orientation change handling maintains layout and user position in document

## Tasks / Subtasks

- [ ] Create touch-optimized interface components (AC: 1)
  - [ ] Implement TouchOptimizedUI component in src/components/mobile/
  - [ ] Ensure minimum 44px touch targets for all interactive elements
  - [ ] Add gesture recognition for swipe, pinch, and tap
  - [ ] Create touch-friendly button and control sizes

- [ ] Implement responsive layout system (AC: 2, 3)
  - [ ] Create TabletLayout component for 768px+ viewports
  - [ ] Implement MobileLayout component for <768px viewports
  - [ ] Add smooth transitions between editor and preview modes
  - [ ] Create swipe navigation for mobile layout

- [ ] Handle virtual keyboard and viewport (AC: 4)
  - [ ] Implement VirtualKeyboardManager service
  - [ ] Handle viewport changes when keyboard appears/disappears
  - [ ] Maintain cursor position during keyboard transitions
  - [ ] Add viewport meta tag optimization

- [ ] Create touch selection and editing tools (AC: 5)
  - [ ] Implement TouchSelectionManager for text manipulation
  - [ ] Add touch-friendly text selection handles
  - [ ] Create context menu for touch devices
  - [ ] Add text formatting shortcuts for mobile

- [ ] Optimize typography and spacing (AC: 6)
  - [ ] Create mobile-optimized typography system
  - [ ] Adjust line spacing and font sizes for mobile reading
  - [ ] Implement responsive spacing for different screen sizes
  - [ ] Add reading mode optimizations

- [ ] Adapt file handling for mobile (AC: 7)
  - [ ] Implement mobile file picker integration
  - [ ] Add cloud storage integration (Google Drive, iCloud, Dropbox)
  - [ ] Create touch-optimized drag-and-drop alternative
  - [ ] Handle mobile browser file access limitations

- [ ] Optimize mobile performance (AC: 8)
  - [ ] Implement mobile-specific performance optimizations
  - [ ] Add touch event throttling for smooth interactions
  - [ ] Optimize rendering for mobile GPUs
  - [ ] Create mobile-specific caching strategies

- [ ] Handle orientation changes (AC: 9)
  - [ ] Implement OrientationManager service
  - [ ] Maintain layout state during orientation changes
  - [ ] Preserve cursor position and scroll position
  - [ ] Adjust layout for landscape/portrait modes

## Dev Notes

### Touch-Optimized Interface Design
```typescript
// Touch-Optimized Component System
interface TouchTarget {
  minSize: number; // 44px minimum
  padding: number;
  hitArea: number; // Extended hit area
}

class TouchOptimizedUI {
  private readonly MIN_TOUCH_TARGET = 44; // px
  private readonly RECOMMENDED_TOUCH_TARGET = 48; // px
  
  createTouchButton(config: ButtonConfig): HTMLElement {
    const button = document.createElement('button');
    
    // Ensure minimum touch target size
    const size = Math.max(config.size || this.RECOMMENDED_TOUCH_TARGET, this.MIN_TOUCH_TARGET);
    button.style.minWidth = `${size}px`;
    button.style.minHeight = `${size}px`;
    
    // Add appropriate padding and margins for easy touching
    button.style.padding = '12px 16px';
    button.style.margin = '4px';
    
    // Add touch-friendly styling
    button.classList.add('touch-optimized', 'select-none');
    
    return button;
  }
  
  addTouchFeedback(element: HTMLElement): void {
    element.addEventListener('touchstart', (e) => {
      element.classList.add('touch-active');
    });
    
    element.addEventListener('touchend', (e) => {
      setTimeout(() => {
        element.classList.remove('touch-active');
      }, 150);
    });
    
    element.addEventListener('touchcancel', (e) => {
      element.classList.remove('touch-active');
    });
  }
}
```

### Responsive Layout System
```typescript
// Mobile and Tablet Layout Management
interface LayoutMode {
  type: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  viewport: {
    width: number;
    height: number;
  };
}

class ResponsiveLayoutManager {
  private currentMode: LayoutMode;
  private listeners: ((mode: LayoutMode) => void)[] = [];
  
  constructor() {
    this.detectLayoutMode();
    this.setupEventListeners();
  }
  
  private detectLayoutMode(): void {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    let type: LayoutMode['type'];
    if (width < 768) {
      type = 'mobile';
    } else if (width < 1024) {
      type = 'tablet';
    } else {
      type = 'desktop';
    }
    
    this.currentMode = {
      type,
      orientation: width > height ? 'landscape' : 'portrait',
      viewport: { width, height }
    };
  }
  
  private setupEventListeners(): void {
    window.addEventListener('resize', this.handleResize.bind(this));
    window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
  }
  
  private handleResize(): void {
    const oldMode = { ...this.currentMode };
    this.detectLayoutMode();
    
    if (this.hasSignificantLayoutChange(oldMode, this.currentMode)) {
      this.notifyLayoutChange();
    }
  }
  
  private handleOrientationChange(): void {
    // Wait for orientation change to complete
    setTimeout(() => {
      this.handleResize();
    }, 100);
  }
  
  getOptimalEditorConfig(): EditorConfig {
    switch (this.currentMode.type) {
      case 'mobile':
        return {
          showLineNumbers: false,
          wordWrap: true,
          fontSize: 16, // Prevent zoom on iOS
          toolbarPosition: 'bottom',
          previewMode: 'tabs',
          autocorrect: false,
          spellcheck: true
        };
      case 'tablet':
        return {
          showLineNumbers: this.currentMode.orientation === 'landscape',
          wordWrap: true,
          fontSize: 14,
          toolbarPosition: 'top',
          previewMode: this.currentMode.orientation === 'landscape' ? 'split' : 'tabs',
          autocorrect: false,
          spellcheck: true
        };
      default:
        return {
          showLineNumbers: true,
          wordWrap: false,
          fontSize: 14,
          toolbarPosition: 'top',
          previewMode: 'split',
          autocorrect: false,
          spellcheck: false
        };
    }
  }
}
```

### Virtual Keyboard Management
```typescript
// Virtual Keyboard and Viewport Management
class VirtualKeyboardManager {
  private originalViewportHeight: number;
  private keyboardVisible = false;
  private activeElement: HTMLElement | null = null;
  
  constructor() {
    this.originalViewportHeight = window.visualViewport?.height || window.innerHeight;
    this.setupEventListeners();
  }
  
  private setupEventListeners(): void {
    // Modern approach using Visual Viewport API
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', this.handleViewportResize.bind(this));
    } else {
      // Fallback for older browsers
      window.addEventListener('resize', this.handleWindowResize.bind(this));
    }
    
    // Track focus changes
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));
  }
  
  private handleViewportResize(): void {
    if (!window.visualViewport) return;
    
    const heightDiff = this.originalViewportHeight - window.visualViewport.height;
    const keyboardHeight = Math.max(0, heightDiff);
    
    const wasVisible = this.keyboardVisible;
    this.keyboardVisible = keyboardHeight > 100; // Threshold for keyboard detection
    
    if (this.keyboardVisible !== wasVisible) {
      this.handleKeyboardToggle(this.keyboardVisible, keyboardHeight);
    }
    
    if (this.keyboardVisible && this.activeElement) {
      this.ensureElementVisible(this.activeElement, keyboardHeight);
    }
  }
  
  private handleKeyboardToggle(visible: boolean, keyboardHeight: number): void {
    const appContainer = document.getElementById('app');
    if (!appContainer) return;
    
    if (visible) {
      // Adjust app container for keyboard
      appContainer.style.paddingBottom = `${keyboardHeight}px`;
      appContainer.classList.add('keyboard-visible');
    } else {
      // Reset app container
      appContainer.style.paddingBottom = '';
      appContainer.classList.remove('keyboard-visible');
    }
    
    // Notify components of keyboard state change
    window.dispatchEvent(new CustomEvent('keyboard-toggle', {
      detail: { visible, keyboardHeight }
    }));
  }
  
  private ensureElementVisible(element: HTMLElement, keyboardHeight: number): void {
    const rect = element.getBoundingClientRect();
    const viewportHeight = window.visualViewport?.height || window.innerHeight;
    const visibleBottom = viewportHeight - keyboardHeight;
    
    if (rect.bottom > visibleBottom) {
      const scrollOffset = rect.bottom - visibleBottom + 20; // 20px padding
      window.scrollBy(0, scrollOffset);
    }
  }
  
  optimizeInputForMobile(input: HTMLInputElement | HTMLTextAreaElement): void {
    // Prevent zoom on iOS
    input.style.fontSize = '16px';
    
    // Optimize for mobile keyboards
    if (input instanceof HTMLTextAreaElement) {
      input.setAttribute('autocorrect', 'off');
      input.setAttribute('autocapitalize', 'sentences');
      input.setAttribute('spellcheck', 'true');
    }
    
    // Handle composition events for better international input
    input.addEventListener('compositionstart', () => {
      input.classList.add('composing');
    });
    
    input.addEventListener('compositionend', () => {
      input.classList.remove('composing');
    });
  }
}
```

### Touch Selection and Editing
```typescript
// Touch-Based Text Selection and Editing
class TouchSelectionManager {
  private selectionHandles: HTMLElement[] = [];
  private contextMenu: HTMLElement | null = null;
  private activeSelection: Selection | null = null;
  
  initializeTouchSelection(editor: HTMLElement): void {
    this.createSelectionHandles();
    this.createContextMenu();
    
    editor.addEventListener('touchstart', this.handleTouchStart.bind(this));
    editor.addEventListener('touchmove', this.handleTouchMove.bind(this));
    editor.addEventListener('touchend', this.handleTouchEnd.bind(this));
    
    // Long press for text selection
    let longPressTimer: number;
    editor.addEventListener('touchstart', (e) => {
      longPressTimer = window.setTimeout(() => {
        this.startTextSelection(e);
      }, 500);
    });
    
    editor.addEventListener('touchend', () => {
      clearTimeout(longPressTimer);
    });
    
    editor.addEventListener('touchmove', () => {
      clearTimeout(longPressTimer);
    });
  }
  
  private createSelectionHandles(): void {
    // Create start handle
    const startHandle = document.createElement('div');
    startHandle.className = 'selection-handle selection-handle-start';
    startHandle.innerHTML = '●';
    
    // Create end handle
    const endHandle = document.createElement('div');
    endHandle.className = 'selection-handle selection-handle-end';
    endHandle.innerHTML = '●';
    
    this.selectionHandles = [startHandle, endHandle];
    
    // Add drag functionality to handles
    this.selectionHandles.forEach(handle => {
      handle.addEventListener('touchstart', this.handleHandleDragStart.bind(this));
      handle.addEventListener('touchmove', this.handleHandleDrag.bind(this));
      handle.addEventListener('touchend', this.handleHandleDragEnd.bind(this));
    });
    
    document.body.appendChild(startHandle);
    document.body.appendChild(endHandle);
  }
  
  private createContextMenu(): void {
    this.contextMenu = document.createElement('div');
    this.contextMenu.className = 'touch-context-menu';
    
    const actions = [
      { label: 'Cut', action: 'cut', icon: '✂️' },
      { label: 'Copy', action: 'copy', icon: '📋' },
      { label: 'Paste', action: 'paste', icon: '📌' },
      { label: 'Bold', action: 'bold', icon: 'B' },
      { label: 'Italic', action: 'italic', icon: 'I' },
      { label: 'Link', action: 'link', icon: '🔗' }
    ];
    
    actions.forEach(({ label, action, icon }) => {
      const button = document.createElement('button');
      button.className = 'context-menu-button';
      button.innerHTML = `<span class="icon">${icon}</span><span class="label">${label}</span>`;
      button.addEventListener('touchend', (e) => {
        e.preventDefault();
        this.executeAction(action);
        this.hideContextMenu();
      });
      
      this.contextMenu.appendChild(button);
    });
    
    document.body.appendChild(this.contextMenu);
  }
  
  private startTextSelection(event: TouchEvent): void {
    const touch = event.touches[0];
    const element = document.elementFromPoint(touch.clientX, touch.clientY);
    
    if (element && this.isEditableElement(element)) {
      // Start selection at touch point
      const range = this.createRangeFromPoint(touch.clientX, touch.clientY);
      if (range) {
        const selection = window.getSelection();
        selection?.removeAllRanges();
        selection?.addRange(range);
        this.activeSelection = selection;
        
        this.showSelectionHandles();
        this.showContextMenu(touch.clientX, touch.clientY);
      }
    }
  }
  
  private showSelectionHandles(): void {
    if (!this.activeSelection || this.activeSelection.rangeCount === 0) return;
    
    const range = this.activeSelection.getRangeAt(0);
    const startRect = range.getClientRects()[0];
    const endRect = range.getClientRects()[range.getClientRects().length - 1];
    
    if (startRect && endRect) {
      // Position start handle
      this.selectionHandles[0].style.left = `${startRect.left}px`;
      this.selectionHandles[0].style.top = `${startRect.top - 20}px`;
      this.selectionHandles[0].style.display = 'block';
      
      // Position end handle
      this.selectionHandles[1].style.left = `${endRect.right}px`;
      this.selectionHandles[1].style.top = `${endRect.bottom}px`;
      this.selectionHandles[1].style.display = 'block';
    }
  }
  
  private showContextMenu(x: number, y: number): void {
    if (!this.contextMenu) return;
    
    this.contextMenu.style.left = `${x}px`;
    this.contextMenu.style.top = `${y - 60}px`;
    this.contextMenu.style.display = 'flex';
    
    // Hide after delay if no interaction
    setTimeout(() => {
      if (this.contextMenu && !this.contextMenu.matches(':hover')) {
        this.hideContextMenu();
      }
    }, 3000);
  }
  
  private executeAction(action: string): void {
    if (!this.activeSelection) return;
    
    switch (action) {
      case 'cut':
        document.execCommand('cut');
        break;
      case 'copy':
        document.execCommand('copy');
        break;
      case 'paste':
        document.execCommand('paste');
        break;
      case 'bold':
        this.wrapSelection('**', '**');
        break;
      case 'italic':
        this.wrapSelection('*', '*');
        break;
      case 'link':
        this.createLink();
        break;
    }
  }
  
  private wrapSelection(prefix: string, suffix: string): void {
    if (!this.activeSelection || this.activeSelection.rangeCount === 0) return;
    
    const range = this.activeSelection.getRangeAt(0);
    const selectedText = range.toString();
    const wrappedText = `${prefix}${selectedText}${suffix}`;
    
    range.deleteContents();
    range.insertNode(document.createTextNode(wrappedText));
  }
}
```

### Mobile File Handling
```typescript
// Mobile-Optimized File Operations
class MobileFileManager {
  private cloudProviders = new Map<string, CloudProvider>();
  
  constructor() {
    this.initializeCloudProviders();
  }
  
  private initializeCloudProviders(): void {
    // Initialize supported cloud storage providers
    this.cloudProviders.set('googledrive', new GoogleDriveProvider());
    this.cloudProviders.set('icloud', new iCloudProvider());
    this.cloudProviders.set('dropbox', new DropboxProvider());
  }
  
  async openFilePicker(): Promise<File | null> {
    // Check if native file API is available
    if ('showOpenFilePicker' in window) {
      try {
        const [fileHandle] = await (window as any).showOpenFilePicker({
          types: [{
            description: 'Markdown files',
            accept: { 'text/markdown': ['.md', '.markdown'] }
          }]
        });
        
        return await fileHandle.getFile();
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('File picker error:', error);
        }
        return null;
      }
    }
    
    // Fallback to traditional file input
    return this.showTraditionalFilePicker();
  }
  
  private showTraditionalFilePicker(): Promise<File | null> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.md,.markdown,text/markdown';
      input.multiple = false;
      
      input.addEventListener('change', (e) => {
        const target = e.target as HTMLInputElement;
        const file = target.files?.[0];
        resolve(file || null);
      });
      
      // Trigger file picker
      input.click();
    });
  }
  
  createMobileDragDrop(container: HTMLElement): void {
    // Create a touch-friendly drop zone for mobile
    const dropZone = document.createElement('div');
    dropZone.className = 'mobile-drop-zone';
    dropZone.innerHTML = `
      <div class="drop-zone-content">
        <div class="drop-icon">📁</div>
        <div class="drop-text">Tap to select file</div>
        <div class="drop-subtext">or drag from file manager</div>
      </div>
    `;
    
    dropZone.addEventListener('touchend', async (e) => {
      e.preventDefault();
      const file = await this.openFilePicker();
      if (file) {
        this.handleFileLoad(file);
      }
    });
    
    // Still support drag and drop for devices that support it
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', () => {
      dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('drag-over');
      
      const files = e.dataTransfer?.files;
      if (files && files.length > 0) {
        this.handleFileLoad(files[0]);
      }
    });
    
    container.appendChild(dropZone);
  }
  
  async openFromCloudStorage(provider: string): Promise<File | null> {
    const cloudProvider = this.cloudProviders.get(provider);
    if (!cloudProvider) {
      throw new Error(`Unsupported cloud provider: ${provider}`);
    }
    
    try {
      return await cloudProvider.openFile();
    } catch (error) {
      console.error(`Error opening file from ${provider}:`, error);
      return null;
    }
  }
  
  async saveToCloudStorage(provider: string, content: string, filename: string): Promise<boolean> {
    const cloudProvider = this.cloudProviders.get(provider);
    if (!cloudProvider) {
      throw new Error(`Unsupported cloud provider: ${provider}`);
    }
    
    try {
      await cloudProvider.saveFile(content, filename);
      return true;
    } catch (error) {
      console.error(`Error saving file to ${provider}:`, error);
      return false;
    }
  }
}
```

## Testing
### Testing Standards
- **Touch Tests:** Test all touch interactions and gestures
- **Responsive Tests:** Verify layouts across different screen sizes
- **Performance Tests:** Measure mobile performance and battery usage
- **Accessibility Tests:** Test with mobile screen readers and assistive technology

### Key Test Scenarios
1. Touch target sizes meet 44px minimum requirement
2. Tablet tabbed interface switches smoothly between editor/preview
3. Mobile stacked layout with swipe navigation
4. Virtual keyboard handling maintains proper viewport
5. Touch text selection and context menu functionality
6. File picker integration with mobile browsers
7. Orientation changes preserve layout and position
8. Performance optimization for smooth mobile experience

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*