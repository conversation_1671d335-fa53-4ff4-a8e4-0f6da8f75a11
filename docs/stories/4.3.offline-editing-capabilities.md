# <!-- Powered by BMAD™ Core -->

# Story 4.3: Offline Editing Capabilities

## Status
Draft

## Story
**As a** user,
**I want** to continue editing my markdown files even when offline,
**so that** connectivity issues don't interrupt my writing workflow.

## Acceptance Criteria
1. Service worker caches all application assets for offline functionality
2. File content persists locally using IndexedDB for reliable offline storage
3. Offline editing maintains full functionality: editing, preview, save to local storage
4. Network status indicators inform users about online/offline state
5. Offline changes persist and remain available when connectivity returns
6. File import/export works offline using browser download/upload capabilities
7. Preference and theme settings persist offline and sync when online
8. Clear user feedback about offline capabilities and limitations
9. Data synchronization strategies prepared for future cloud storage integration

## Tasks / Subtasks

- [ ] Enhance service worker for comprehensive offline caching (AC: 1)
  - [ ] Expand service worker from Story 4.2 with offline-first strategies
  - [ ] Cache all critical application assets and resources
  - [ ] Implement intelligent cache management for offline operations
  - [ ] Add fallback mechanisms for uncached resources

- [ ] Implement IndexedDB storage system (AC: 2, 5)
  - [ ] Create OfflineStorageManager service using IndexedDB
  - [ ] Design database schema for file content and metadata
  - [ ] Implement CRUD operations for offline file management
  - [ ] Add data versioning and migration strategies

- [ ] Create offline editing functionality (AC: 3)
  - [ ] Ensure editor works completely offline
  - [ ] Maintain preview rendering without network dependencies
  - [ ] Implement offline save/autosave functionality
  - [ ] Handle offline state transitions gracefully

- [ ] Build network status monitoring (AC: 4, 8)
  - [ ] Create NetworkStatusManager for connection monitoring
  - [ ] Add visual indicators for online/offline status
  - [ ] Implement user notifications for connectivity changes
  - [ ] Provide clear feedback about offline limitations

- [ ] Implement offline file operations (AC: 6)
  - [ ] Enable file import/export without network connectivity
  - [ ] Create offline file browser for locally stored files
  - [ ] Implement offline download functionality
  - [ ] Handle file operations in offline mode

- [ ] Create settings synchronization (AC: 7)
  - [ ] Implement offline settings storage and retrieval
  - [ ] Sync preferences when connectivity is restored
  - [ ] Handle settings conflicts between offline/online states
  - [ ] Maintain settings consistency across sessions

- [ ] Prepare for future cloud synchronization (AC: 9)
  - [ ] Design conflict resolution strategies
  - [ ] Create sync protocol for future cloud integration
  - [ ] Implement change tracking and merging capabilities
  - [ ] Add background sync foundation

## Dev Notes

### Enhanced Service Worker for Offline-First
```typescript
// Enhanced Service Worker for Comprehensive Offline Support
const CACHE_VERSION = 'v1.2.0';
const CACHE_NAMES = {
  static: `mdedit-static-${CACHE_VERSION}`,
  dynamic: `mdedit-dynamic-${CACHE_VERSION}`,
  fonts: `mdedit-fonts-${CACHE_VERSION}`,
  images: `mdedit-images-${CACHE_VERSION}`
};

// Comprehensive list of resources to cache for offline functionality
const OFFLINE_RESOURCES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/offline.html',
  
  // Core application files
  '/static/js/main.js',
  '/static/js/worker.js',
  '/static/css/main.css',
  
  // Essential libraries (if served locally)
  '/static/js/marked.min.js',
  '/static/js/codemirror.min.js',
  
  // Icons and images
  '/static/media/icons/icon-192x192.png',
  '/static/media/icons/icon-512x512.png',
  '/static/media/images/empty-state.svg',
  
  // Fonts
  '/static/fonts/inter.woff2',
  '/static/fonts/fira-code.woff2'
];

// Install event - comprehensive caching
self.addEventListener('install', (event) => {
  console.log('[SW] Installing enhanced offline service worker');
  
  event.waitUntil(
    Promise.all([
      // Cache static resources
      caches.open(CACHE_NAMES.static)
        .then(cache => cache.addAll(OFFLINE_RESOURCES)),
      
      // Pre-cache critical runtime resources
      caches.open(CACHE_NAMES.dynamic)
        .then(cache => {
          // Pre-cache some dynamic content
          return cache.addAll([
            '/api/themes/default',
            '/api/preferences/default'
          ]).catch(() => {
            // Ignore if API endpoints don't exist yet
            console.log('[SW] Skipping API pre-caching');
          });
        })
    ]).then(() => {
      return self.skipWaiting();
    })
  );
});

// Enhanced fetch handler with offline-first strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests and cross-origin requests
  if (request.method !== 'GET' || url.origin !== location.origin) {
    return;
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleAPIRequest(request));
  } else if (url.pathname.match(/\.(png|jpg|jpeg|svg|gif|ico)$/)) {
    event.respondWith(handleImageRequest(request));
  } else if (url.pathname.match(/\.(woff|woff2|ttf|eot)$/)) {
    event.respondWith(handleFontRequest(request));
  } else {
    event.respondWith(handleDocumentRequest(request));
  }
});

// Handle API requests with offline fallback
async function handleAPIRequest(request) {
  try {
    // Try network first for API requests
    const response = await fetch(request);
    
    if (response.ok) {
      // Cache successful API responses
      const cache = await caches.open(CACHE_NAMES.dynamic);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    // Network failed, try cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback for API requests
    return new Response(JSON.stringify({
      error: 'offline',
      message: 'This feature requires an internet connection'
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle document requests with offline support
async function handleDocumentRequest(request) {
  // Try cache first for better performance
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    // Serve from cache and update in background
    fetchAndCache(request);
    return cachedResponse;
  }
  
  try {
    const response = await fetch(request);
    
    if (response.ok) {
      const cache = await caches.open(CACHE_NAMES.dynamic);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    // Return offline fallback page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/offline.html');
    }
    
    throw error;
  }
}
```

### IndexedDB Storage Manager
```typescript
// Comprehensive Offline Storage with IndexedDB
interface StoredFile {
  id: string;
  filename: string;
  content: string;
  lastModified: number;
  size: number;
  created: number;
  version: number;
  synced: boolean;
  changes: Change[];
}

interface Change {
  id: string;
  timestamp: number;
  type: 'insert' | 'delete' | 'update';
  position: number;
  content: string;
  length: number;
}

interface StorageConfig {
  dbName: string;
  version: number;
  stores: string[];
}

class OfflineStorageManager {
  private db: IDBDatabase | null = null;
  private config: StorageConfig = {
    dbName: 'MDEditOfflineDB',
    version: 1,
    stores: ['files', 'preferences', 'themes', 'changes']
  };
  
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.dbName, this.config.version);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create files store
        if (!db.objectStoreNames.contains('files')) {
          const filesStore = db.createObjectStore('files', { keyPath: 'id' });
          filesStore.createIndex('filename', 'filename', { unique: false });
          filesStore.createIndex('lastModified', 'lastModified', { unique: false });
        }
        
        // Create preferences store
        if (!db.objectStoreNames.contains('preferences')) {
          db.createObjectStore('preferences', { keyPath: 'key' });
        }
        
        // Create themes store
        if (!db.objectStoreNames.contains('themes')) {
          db.createObjectStore('themes', { keyPath: 'id' });
        }
        
        // Create changes store for sync
        if (!db.objectStoreNames.contains('changes')) {
          const changesStore = db.createObjectStore('changes', { keyPath: 'id' });
          changesStore.createIndex('fileId', 'fileId', { unique: false });
          changesStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }
  
  async saveFile(file: Omit<StoredFile, 'id' | 'created' | 'version'>): Promise<string> {
    if (!this.db) throw new Error('Database not initialized');
    
    const id = crypto.randomUUID();
    const storedFile: StoredFile = {
      ...file,
      id,
      created: Date.now(),
      version: 1,
      synced: false,
      changes: []
    };
    
    const transaction = this.db.transaction(['files'], 'readwrite');
    const store = transaction.objectStore('files');
    
    await new Promise<void>((resolve, reject) => {
      const request = store.add(storedFile);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
    
    // Track creation change for sync
    await this.trackChange({
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      type: 'insert',
      position: 0,
      content: file.content,
      length: file.content.length
    }, id);
    
    return id;
  }
  
  async updateFile(id: string, updates: Partial<StoredFile>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const transaction = this.db.transaction(['files'], 'readwrite');
    const store = transaction.objectStore('files');
    
    // Get existing file
    const existingFile = await new Promise<StoredFile>((resolve, reject) => {
      const request = store.get(id);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
    
    if (!existingFile) {
      throw new Error('File not found');
    }
    
    // Create updated file
    const updatedFile: StoredFile = {
      ...existingFile,
      ...updates,
      lastModified: Date.now(),
      version: existingFile.version + 1,
      synced: false
    };
    
    // Save updated file
    await new Promise<void>((resolve, reject) => {
      const request = store.put(updatedFile);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
    
    // Track content changes for sync
    if (updates.content && updates.content !== existingFile.content) {
      const changes = this.calculateChanges(existingFile.content, updates.content);
      for (const change of changes) {
        await this.trackChange(change, id);
      }
    }
  }
  
  async getFile(id: string): Promise<StoredFile | null> {
    if (!this.db) throw new Error('Database not initialized');
    
    const transaction = this.db.transaction(['files'], 'readonly');
    const store = transaction.objectStore('files');
    
    return new Promise((resolve, reject) => {
      const request = store.get(id);
      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }
  
  async getAllFiles(): Promise<StoredFile[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const transaction = this.db.transaction(['files'], 'readonly');
    const store = transaction.objectStore('files');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
  
  async deleteFile(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const transaction = this.db.transaction(['files'], 'readwrite');
    const store = transaction.objectStore('files');
    
    await new Promise<void>((resolve, reject) => {
      const request = store.delete(id);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
    
    // Track deletion for sync
    await this.trackChange({
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      type: 'delete',
      position: 0,
      content: '',
      length: 0
    }, id);
  }
  
  private async trackChange(change: Change, fileId: string): Promise<void> {
    if (!this.db) return;
    
    const transaction = this.db.transaction(['changes'], 'readwrite');
    const store = transaction.objectStore('changes');
    
    const changeRecord = {
      ...change,
      fileId,
      id: crypto.randomUUID()
    };
    
    await new Promise<void>((resolve, reject) => {
      const request = store.add(changeRecord);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }
  
  private calculateChanges(oldContent: string, newContent: string): Change[] {
    // Simple diff implementation - could be enhanced with more sophisticated algorithm
    const changes: Change[] = [];
    
    if (oldContent !== newContent) {
      changes.push({
        id: crypto.randomUUID(),
        timestamp: Date.now(),
        type: 'update',
        position: 0,
        content: newContent,
        length: newContent.length
      });
    }
    
    return changes;
  }
  
  // Get pending changes for sync
  async getPendingChanges(): Promise<(Change & { fileId: string })[]> {
    if (!this.db) return [];
    
    const transaction = this.db.transaction(['changes'], 'readonly');
    const store = transaction.objectStore('changes');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}
```

### Network Status Management
```typescript
// Network Status Monitoring and User Feedback
interface NetworkStatus {
  online: boolean;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
}

class NetworkStatusManager {
  private status: NetworkStatus;
  private listeners: ((status: NetworkStatus) => void)[] = [];
  private lastOnlineTime: number = Date.now();
  private offlineNotificationShown = false;
  
  constructor() {
    this.status = this.getCurrentStatus();
    this.setupEventListeners();
  }
  
  private getCurrentStatus(): NetworkStatus {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    return {
      online: navigator.onLine,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt,
      saveData: connection?.saveData
    };
  }
  
  private setupEventListeners(): void {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
    
    // Listen to connection changes if supported
    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', this.handleConnectionChange.bind(this));
    }
  }
  
  private handleOnline(): void {
    const wasOffline = !this.status.online;
    this.status = this.getCurrentStatus();
    this.lastOnlineTime = Date.now();
    
    if (wasOffline) {
      this.showOnlineNotification();
      this.offlineNotificationShown = false;
    }
    
    this.notifyListeners();
  }
  
  private handleOffline(): void {
    this.status = this.getCurrentStatus();
    
    if (!this.offlineNotificationShown) {
      this.showOfflineNotification();
      this.offlineNotificationShown = true;
    }
    
    this.notifyListeners();
  }
  
  private handleConnectionChange(): void {
    const oldStatus = { ...this.status };
    this.status = this.getCurrentStatus();
    
    // Notify if significant change
    if (oldStatus.effectiveType !== this.status.effectiveType) {
      this.notifyListeners();
    }
  }
  
  private showOfflineNotification(): void {
    // Create non-intrusive offline notification
    const notification = document.createElement('div');
    notification.className = 'network-status-notification offline';
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">📴</div>
        <div class="notification-text">
          <div class="notification-title">You're offline</div>
          <div class="notification-message">Don't worry, you can keep editing. Your changes will be saved locally.</div>
        </div>
        <button class="notification-dismiss" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-dismiss after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 10000);
  }
  
  private showOnlineNotification(): void {
    const offlineTime = Date.now() - this.lastOnlineTime;
    const offlineDuration = this.formatDuration(offlineTime);
    
    const notification = document.createElement('div');
    notification.className = 'network-status-notification online';
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">🌐</div>
        <div class="notification-text">
          <div class="notification-title">You're back online</div>
          <div class="notification-message">You were offline for ${offlineDuration}. Syncing your changes...</div>
        </div>
        <button class="notification-dismiss" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }
  
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
  
  getStatus(): NetworkStatus {
    return { ...this.status };
  }
  
  isOnline(): boolean {
    return this.status.online;
  }
  
  hasGoodConnection(): boolean {
    if (!this.status.online) return false;
    
    // Consider connection good if:
    // - Effective type is 4g or better
    // - RTT is reasonable (< 300ms)
    // - Downlink is adequate (> 1 Mbps)
    const hasGoodType = !this.status.effectiveType || ['4g', '5g'].includes(this.status.effectiveType);
    const hasGoodRTT = !this.status.rtt || this.status.rtt < 300;
    const hasGoodDownlink = !this.status.downlink || this.status.downlink > 1;
    
    return hasGoodType && hasGoodRTT && hasGoodDownlink;
  }
  
  onChange(listener: (status: NetworkStatus) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
  
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.status));
  }
}
```

### Offline File Operations
```typescript
// Complete Offline File Management
class OfflineFileManager {
  private storageManager: OfflineStorageManager;
  private networkManager: NetworkStatusManager;
  
  constructor() {
    this.storageManager = new OfflineStorageManager();
    this.networkManager = new NetworkStatusManager();
    this.initialize();
  }
  
  private async initialize(): Promise<void> {
    await this.storageManager.initialize();
    
    // Set up auto-save for offline editing
    this.setupAutoSave();
    
    // Listen for network changes to sync
    this.networkManager.onChange((status) => {
      if (status.online) {
        this.syncPendingChanges();
      }
    });
  }
  
  async createNewFile(filename?: string): Promise<string> {
    const newFile = {
      filename: filename || `untitled-${Date.now()}.md`,
      content: '',
      lastModified: Date.now(),
      size: 0
    };
    
    return await this.storageManager.saveFile(newFile);
  }
  
  async openFile(file: File): Promise<string> {
    const content = await this.readFileContent(file);
    
    const storedFile = {
      filename: file.name,
      content,
      lastModified: Date.now(),
      size: content.length
    };
    
    return await this.storageManager.saveFile(storedFile);
  }
  
  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = () => {
        resolve(reader.result as string);
      };
      
      reader.onerror = () => {
        reject(reader.error);
      };
      
      reader.readAsText(file);
    });
  }
  
  async saveFile(id: string, content: string, filename?: string): Promise<void> {
    const updates: Partial<StoredFile> = {
      content,
      size: content.length
    };
    
    if (filename) {
      updates.filename = filename;
    }
    
    await this.storageManager.updateFile(id, updates);
    
    // Show save confirmation
    this.showSaveNotification(filename || 'File');
  }
  
  async downloadFile(id: string): Promise<void> {
    const file = await this.storageManager.getFile(id);
    if (!file) {
      throw new Error('File not found');
    }
    
    // Create blob and download
    const blob = new Blob([file.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = file.filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Cleanup
    URL.revokeObjectURL(url);
  }
  
  async getFileList(): Promise<StoredFile[]> {
    return await this.storageManager.getAllFiles();
  }
  
  async deleteFile(id: string): Promise<void> {
    await this.storageManager.deleteFile(id);
  }
  
  private setupAutoSave(): void {
    let autoSaveTimer: number | null = null;
    let currentFileId: string | null = null;
    let currentContent: string = '';
    
    // Auto-save every 30 seconds
    const scheduleAutoSave = () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
      
      autoSaveTimer = window.setTimeout(() => {
        if (currentFileId && currentContent) {
          this.saveFile(currentFileId, currentContent);
        }
      }, 30000);
    };
    
    // Listen for content changes
    window.addEventListener('content-changed', (event: any) => {
      currentFileId = event.detail.fileId;
      currentContent = event.detail.content;
      scheduleAutoSave();
    });
  }
  
  private showSaveNotification(filename: string): void {
    const notification = document.createElement('div');
    notification.className = 'save-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">💾</div>
        <div class="notification-text">${filename} saved offline</div>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
      notification.classList.add('fade-out');
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 300);
    }, 3000);
  }
  
  private async syncPendingChanges(): Promise<void> {
    if (!this.networkManager.hasGoodConnection()) {
      return;
    }
    
    const pendingChanges = await this.storageManager.getPendingChanges();
    
    if (pendingChanges.length === 0) {
      return;
    }
    
    console.log(`Syncing ${pendingChanges.length} pending changes`);
    
    // Show sync notification
    this.showSyncNotification(pendingChanges.length);
    
    // Here you would implement actual sync logic with your backend
    // For now, we'll just mark everything as synced
    setTimeout(() => {
      this.completeSyncNotification();
    }, 2000);
  }
  
  private showSyncNotification(count: number): void {
    const notification = document.createElement('div');
    notification.id = 'sync-notification';
    notification.className = 'sync-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">🔄</div>
        <div class="notification-text">Syncing ${count} changes...</div>
        <div class="sync-progress">
          <div class="sync-progress-bar"></div>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);
  }
  
  private completeSyncNotification(): void {
    const notification = document.getElementById('sync-notification');
    if (notification) {
      notification.innerHTML = `
        <div class="notification-content">
          <div class="notification-icon">✅</div>
          <div class="notification-text">All changes synced</div>
        </div>
      `;
      
      setTimeout(() => {
        notification.remove();
      }, 2000);
    }
  }
}
```

## Testing
### Testing Standards
- **Offline Tests:** Test complete offline functionality across browsers
- **Storage Tests:** Verify IndexedDB operations and data persistence
- **Sync Tests:** Test data synchronization when connectivity returns
- **Network Tests:** Verify network status detection and handling

### Key Test Scenarios
1. Complete offline editing workflow without network
2. IndexedDB storage and retrieval of files and preferences
3. Network status detection and user notifications
4. Offline file import/export functionality
5. Auto-save and data persistence during offline sessions
6. Settings synchronization between offline and online states
7. Service worker caching of all essential resources
8. Graceful handling of network transitions

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*Results from QA Agent review will be populated here after story completion*