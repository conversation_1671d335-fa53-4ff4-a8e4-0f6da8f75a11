# Animation & Micro-interactions

## Motion Principles
**Design Philosophy:** Subtle, purposeful motion that enhances usability without distraction.

## Key Animations

### File Drag & Drop Feedback
- **Drag Enter:** Drop zone border transitions from dashed to solid over 150ms with ease-out
- **Drop Success:** Green checkmark appears with scale animation over 400ms
- **File Processing:** Progress bar fills with 200ms ease-out segments

### Editor & Preview Synchronization
- **Preview Update:** Subtle opacity flash when content updates (200ms ease-in-out)
- **Scroll Sync:** Smooth scroll with 300ms ease-out
- **Syntax Highlighting:** Color changes fade in over 100ms

## Performance-Conscious Animation
```css
/* GPU-accelerated properties only */
.smooth-transition {
  transform: translateX(0); /* GPU accelerated */
  opacity: 1; /* GPU accelerated */
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}
```
