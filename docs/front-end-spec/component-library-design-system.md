# Component Library / Design System

## Design System Approach
**Strategy:** Minimal component library focused on markdown editing workflow with progressive enhancement support.

## Core Components

### Drop Zone Component
**Variants:**
- **Default State:** Dashed border, centered content, hover-ready
- **Drag Hover:** Solid border, highlighted background, animated feedback
- **Invalid Drag:** Red dashed border, warning icons, error messaging
- **Loading:** Progress indicator, file processing feedback

### Editor Wrapper Component
**Purpose:** Container for both basic textarea (Epic 1) and CodeMirror (Epic 2+) with consistent interface

**Variants:**
- **Basic Mode:** HTML textarea with minimal styling
- **Enhanced Mode:** CodeMirror integration with syntax highlighting
- **Mobile Mode:** Touch-optimized with larger targets

### Toolbar Component
**Purpose:** Contextual actions and controls adapted to current Epic functionality

```
Toolbar Evolution:
Epic 1:  [📄] [📥] [⚙️] [?]
Epic 2:  [📄] [🔍] [⚙️] [💾] [📥] [?] [═══] [🌙☀️] [📊]
```
