# User Flows

## Flow 1: File Drag-and-Drop Workflow

**User Goal:** Load a markdown file into the editor using drag-and-drop interface

**Entry Points:** 
- Empty state landing page
- Active editing view (replacing current file)
- Direct browser navigation to MDEdit URL

**Success Criteria:** 
- File content displays in editor within 2 seconds
- Preview renders correctly with proper markdown formatting
- User can immediately begin editing

### Flow Diagram

```mermaid
graph TD
    A[User visits MDEdit] --> B[Empty State Display]
    B --> C{File dragged over interface?}
    C -->|No| B
    C -->|Yes| D[Visual drag feedback - highlight drop zone]
    D --> E{File dropped?}
    E -->|No drag leave| B
    E -->|Yes| F[File validation]
    F --> G{Valid markdown file?}
    G -->|No| H[Error state - invalid file type]
    G -->|Yes| I{File size check}
    I -->|>200KB| J[Warning dialog - proceed/cancel]
    I -->|<200KB| K[Loading indicator]
    J -->|Cancel| B
    J -->|Proceed| K
    K --> L[Parse file content]
    L --> M[Display in split-pane editor]
    M --> N[Generate initial preview]
    N --> O[Ready for editing]
    H --> P[Return to empty state with error message]
    P --> B
    
    style A fill:#e8f5e8
    style O fill:#e8f5e8
    style H fill:#ffebee
    style P fill:#ffebee
```

### Edge Cases & Error Handling
- **Large file performance:** Files >50KB use Web Worker parsing with progress indicator
- **Encoding issues:** Character encoding detection with fallback options
- **Network interruption:** Not applicable for client-side file processing
- **Browser compatibility:** Fallback to file input dialog for unsupported browsers
- **Multiple file drop:** Only first file processed, clear feedback about single-file limitation
- **Empty file handling:** Display empty editor with placeholder text
- **Corrupted file data:** Graceful error with file reselection option

**Notes:** This flow supports both desktop drag-and-drop and mobile file picker interfaces. Mobile users tap a "Select File" button that triggers the native file browser.

## Flow 2: Real-Time Editing & Preview Synchronization

**User Goal:** Edit markdown content with immediate visual feedback and synchronized scrolling

**Entry Points:**
- After successful file load
- Creating new content from empty state
- Returning to editing after preview-only viewing

**Success Criteria:**
- Preview updates within 100ms of typing
- Scroll positions remain synchronized between panes
- No lag or stuttering during editing

### Flow Diagram

```mermaid
graph TD
    A[User types in editor] --> B[Debounce timer - 100ms]
    B --> C{Content changed?}
    C -->|No| D[No update needed]
    C -->|Yes| E{Document size check}
    E -->|<50KB| F[Parse markdown - main thread]
    E -->|≥50KB| G[Parse markdown - Web Worker]
    F --> H[Update preview DOM]
    G --> H
    H --> I[Scroll synchronization]
    I --> J{User scrolled recently?}
    J -->|Yes| K[Maintain user scroll position]
    J -->|No| L[Auto-sync scroll positions]
    K --> M[Ready for next edit]
    L --> M
    D --> M
    
    style A fill:#e3f2fd
    style M fill:#e8f5e8
```

### Edge Cases & Error Handling
- **Rapid typing performance:** Input buffering prevents dropped keystrokes during heavy parsing
- **Markdown syntax errors:** Preview shows partial rendering with error indicators
- **Scroll conflict resolution:** User-initiated scrolling takes precedence over auto-sync
- **Memory management:** Long editing sessions trigger garbage collection for large documents
- **Focus management:** Editor maintains focus during preview updates

## Flow 3: Mobile Responsive Editing Experience

**User Goal:** Edit markdown files effectively on mobile and tablet devices

**Entry Points:**
- Mobile browser navigation to MDEdit
- PWA app launch on mobile device
- Tablet orientation changes

**Success Criteria:**
- File selection works with mobile file browsers
- Editing interface adapts appropriately to screen size
- Touch interactions feel native and responsive

### Flow Diagram

```mermaid
graph TD
    A[Mobile user accesses MDEdit] --> B{Screen size detection}
    B -->|<768px Mobile| C[Single-pane stacked layout]
    B -->|768-1199px Tablet| D[Tabbed interface layout]
    B -->|≥1200px Desktop| E[Split-pane layout]
    
    C --> F[File selection via button]
    D --> F
    E --> G[Drag-and-drop enabled]
    
    F --> H[Native file picker]
    G --> I[Drag feedback states]
    H --> J[File loaded - mobile editing]
    I --> K[File loaded - desktop editing]
    
    J --> L[Swipe between edit/preview]
    K --> M[Side-by-side edit/preview]
    
    L --> N{Orientation change?}
    N -->|Yes| O[Recalculate layout]
    N -->|No| P[Continue editing]
    O --> P
    M --> P
    
    style A fill:#fff3e0
    style P fill:#e8f5e8
```

### Edge Cases & Error Handling
- **Virtual keyboard handling:** Interface adjusts viewport when keyboard appears
- **Touch target sizing:** All interactive elements meet 44px minimum touch target
- **Orientation change timing:** Layout recalculation waits for orientation completion
- **File picker limitations:** Clear messaging about supported file types
- **Network-dependent features:** Offline editing capabilities clearly communicated

**Notes:** Mobile flow prioritizes touch-first interactions while maintaining feature parity with desktop experience.
