# Information Architecture (IA)

## Site Map / Screen Inventory

```mermaid
graph TD
    A[Empty State Landing] --> B[Active Editing View]
    A --> E[Error States]
    B --> B1[Split-Pane Desktop]
    B --> B2[Tabbed Tablet View]
    B --> B3[Stacked Mobile View]
    B1 --> C[Editor Pane - Left]
    B1 --> D[Preview Pane - Right]
    B2 --> C2[Editor Tab]
    B2 --> D2[Preview Tab]
    B3 --> C3[Editor Section]
    B3 --> D3[Preview Section]
    E --> E1[File Type Error]
    E --> E2[File Size Warning]
    E --> E3[Browser Compatibility]
    B --> F[Download/Save State]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style E fill:#ffebee
```

## Navigation Structure

**Primary Navigation:** Contextual actions within single-page interface - no traditional navigation menu
- File actions (Download/Save) appear when content is loaded
- Settings/preferences accessible via keyboard shortcuts or subtle UI controls
- Help/documentation accessible via `?` key or info icon

**Secondary Navigation:** State-dependent interface elements
- Epic 1: Basic toolbar with Download button
- Epic 2: Enhanced toolbar with view options, search, settings
- Epic 3: Command palette (Ctrl+Shift+P) for advanced features
- Epic 4: PWA install prompt and offline indicators

**Breadcrumb Strategy:** Not applicable for single-file editing interface
- File name display serves as context indicator
- Document position indicator (Epic 2+) shows location within large files
