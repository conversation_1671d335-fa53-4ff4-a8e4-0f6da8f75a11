# Wireframes & Mockups

## Primary Design Files
**Design Tool:** ASCII wireframes and detailed specifications in this document
**Reference:** All wireframes support the progressive enhancement from Epic 1 (basic textarea) to Epic 4 (full PWA)

## Key Screen Layouts

### 1. Empty State Landing (All Devices)

**Purpose:** Welcome users and clearly communicate the drag-and-drop workflow

**Desktop Layout (≥1200px):**
```
┌─────────────────────────────────────────────────────────────┐
│                          MDEdit                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                             │
│    ┌─────────────────────────────────────────────────┐    │
│    │                                                 │    │
│    │        📄 Drop markdown file here              │    │
│    │                                                 │    │
│    │        Drag .md or .txt files to get started   │    │
│    │                                                 │    │
│    │        [Select File Button] (fallback)         │    │
│    │                                                 │    │
│    └─────────────────────────────────────────────────┘    │
│                                                             │
│          Supported: .md, .txt files up to 200KB           │
│                                                             │
│                    [?] Help & Shortcuts                    │
└─────────────────────────────────────────────────────────────┘
```

**Key Elements:**
- Centered drop zone: 600px × 400px with dashed border
- Large file icon and clear call-to-action text
- Fallback "Select File" button for accessibility/compatibility
- Subtle file format guidance below drop zone
- Help access in footer

**Tablet Layout (768-1199px):**
```
┌─────────────────────────────────────────┐
│              MDEdit                     │
├─────────────────────────────────────────┤
│                                         │
│  ┌───────────────────────────────────┐  │
│  │                                   │  │
│  │    📄 Drop or select file        │  │
│  │                                   │  │
│  │    [Select File Button]          │  │
│  │                                   │  │
│  └───────────────────────────────────┘  │
│                                         │
│        .md, .txt up to 200KB           │
└─────────────────────────────────────────┘
```

**Mobile Layout (<768px):**
```
┌─────────────────────┐
│      MDEdit         │
├─────────────────────┤
│                     │
│ ┌─────────────────┐ │
│ │                 │ │
│ │ 📄 Tap to add   │ │
│ │    file         │ │
│ │                 │ │
│ │ [Select File]   │ │
│ │                 │ │
│ └─────────────────┘ │
│                     │
│  .md/.txt 200KB max │
└─────────────────────┘
```

### 2. Split-Pane Active Editing (Desktop)

**Purpose:** Primary editing interface with real-time preview

**Desktop Layout with Epic 1 (Basic Textarea):**
```
┌─────────────────────────────────────────────────────────────┐
│ 📄 filename.md              [Download] [Settings] [Help]    │
├─────────────────────────────────────────────────────────────┤
│                              │                              │
│ # Markdown Content           │ Markdown Content            │
│                              │                              │
│ This is **bold** text        │ This is bold text           │
│ and this is *italic*.        │ and this is italic.         │
│                              │                              │
│ ## Code Example              │ Code Example                │
│                              │                              │
│ ```javascript                │ function hello() {          │
│ function hello() {           │   console.log('world');     │
│   console.log('world');      │ }                           │
│ }                            │                              │
│ ```                          │                              │
│                              │                              │
│ - List item 1                │ • List item 1               │
│ - List item 2                │ • List item 2               │
│                              │                              │
│                              │                              │
│ [Editor Pane - 50%]          │ [Preview Pane - 50%]       │
│                              │                              │
└─────────────────────────────────────────────────────────────┘
```

**Epic 2 Enhancement (CodeMirror):**
```
┌─────────────────────────────────────────────────────────────┐
│ 📄 filename.md    [🔍] [⚙️] [💾] [📥] [?] [═══] [🌙/☀️]    │
├─────────────────────────────────────────────────────────────┤
│   1 │ # Markdown Content     │ Markdown Content            │
│   2 │                        │                              │
│   3 │ This is **bold** text  │ This is bold text           │
│   4 │ and this is *italic*.  │ and this is italic.         │
│   5 │                        │                              │
│   6 │ ## Code Example        │ Code Example                │
│   7 │                        │                              │
│   8 │ ```javascript          │ function hello() {          │
│   9 │ function hello() {     │   console.log('world');     │
│  10 │   console.log('world');│ }                           │
│  11 │ }                      │                              │
│  12 │ ```                    │                              │
│  13 │                        │                              │
│  14 │ - List item 1          │ • List item 1               │
│  15 │ - List item 2          │ • List item 2               │
│     │                        │                              │
│     │ [CodeMirror - 50%]     │ [Synced Preview - 50%]      │
│     │                        │                              │
└─────────────────────────────────────────────────────────────┤
│ Ln 10, Col 15 │ 1.2KB │ Words: 45 │ ⚡ Ready       │ 🔄 Synced │
└─────────────────────────────────────────────────────────────┘
```

**Key Elements:**
- Resizable split pane with drag handle (═══)
- Toolbar with contextual actions
- Line numbers and syntax highlighting (Epic 2+)
- Status bar with document stats
- Scroll synchronization indicators

### 3. Tablet Tabbed Interface

**Purpose:** Efficient editing on medium screens with tab switching

**Tablet Layout:**
```
┌─────────────────────────────────────────┐
│ 📄 filename.md            [⚙️] [📥] [?] │
├─────────────────────────────────────────┤
│ [✏️ Edit] [👁️ Preview]                  │
├─────────────────────────────────────────┤
│                                         │
│ # Markdown Content                      │
│                                         │
│ This is **bold** text and this is      │
│ *italic*.                               │
│                                         │
│ ## Code Example                         │
│                                         │
│ ```javascript                           │
│ function hello() {                      │
│   console.log('world');                 │
│ }                                       │
│ ```                                     │
│                                         │
│ - List item 1                           │
│ - List item 2                           │
│                                         │
│                                         │
│ [Active Tab Content - Full Width]       │
│                                         │
└─────────────────────────────────────────┘
```

**Interaction Notes:**
- Tap tabs to switch between edit/preview modes
- Swipe gestures for quick tab switching
- Preview tab shows same content as desktop preview pane

### 4. Mobile Stacked Interface

**Purpose:** Touch-optimized editing for small screens

**Mobile Layout:**
```
┌─────────────────────┐
│📄 filename.md [⚙️][?]│
├─────────────────────┤
│ Edit ▼ Preview      │
├─────────────────────┤
│                     │
│ # Markdown Content  │
│                     │
│ This is **bold**    │
│ text and this is    │
│ *italic*.           │
│                     │
│ ## Code Example     │
│                     │
│ ```javascript       │
│ function hello() {  │
│   console.log(      │
│     'world');       │
│ }                   │
│ ```                 │
│                     │
│ - List item 1       │
│ - List item 2       │
│                     │
│ [Stacked Content]   │
│ ← Swipe to Preview  │
│                     │
└─────────────────────┘
```

**Key Elements:**
- Dropdown selector for edit/preview mode
- Swipe indicators for gesture navigation
- Touch-optimized text size and spacing
- Simplified toolbar with essential actions only

## Empty States & Error Handling Designs

### 5. Drag-and-Drop Visual Feedback States

**Purpose:** Provide clear visual feedback during drag operations to guide user behavior

**Drag Hover State (Desktop):**
```
┌─────────────────────────────────────────────────────────────┐
│                          MDEdit                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                             │
│    ╔═════════════════════════════════════════════════════╗  │
│    ║                                                     ║  │
│    ║        📄 Drop your markdown file here             ║  │
│    ║                                                     ║  │
│    ║            Release to load file                     ║  │
│    ║                                                     ║  │
│    ║        [Select File Button] (still available)      ║  │
│    ║                                                     ║  │
│    ╚═════════════════════════════════════════════════════╝  │
│                                                             │
│          ✓ Valid file detected - ready to drop            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Invalid File Drag State:**
```
┌─────────────────────────────────────────────────────────────┐
│                          MDEdit                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                             │
│    ┌─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┐  │
│    ┊                                                     ┊  │
│    ┊        🚫 Invalid file type                        ┊  │
│    ┊                                                     ┊  │
│    ┊        Only .md and .txt files supported           ┊  │
│    ┊                                                     ┊  │
│    ┊        [Select File Button]                        ┊  │
│    ┊                                                     ┊  │
│    └─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┘  │
│                                                             │
│          ⚠️ Please select a markdown or text file          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 6. File Processing States

**Loading State with Progress:**
```
┌─────────────────────────────────────────────────────────────┐
│                          MDEdit                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                             │
│    ┌─────────────────────────────────────────────────┐    │
│    │                                                 │    │
│    │              🔄 Loading file...                 │    │
│    │                                                 │    │
│    │        ████████████░░░░░░░░░ 60%               │    │
│    │                                                 │    │
│    │           Processing: filename.md               │    │
│    │              (45.2 KB)                         │    │
│    │                                                 │    │
│    └─────────────────────────────────────────────────┘    │
│                                                             │
│               Parsing content and generating preview...     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Large File Warning Dialog:**
```
┌─────────────────────────────────────────────────────────────┐
│                          MDEdit                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│           ┌─────────────────────────────────────┐          │
│           │ ⚠️  Large File Warning               │          │
│           ├─────────────────────────────────────┤          │
│           │                                     │          │
│           │ This file is 156.8 KB (>200 KB).   │          │
│           │                                     │          │
│           │ Large files may cause:              │          │
│           │ • Slower preview updates            │          │
│           │ • Increased memory usage            │          │
│           │ • Potential browser slowdown        │          │
│           │                                     │          │
│           │ Continue loading this file?         │          │
│           │                                     │          │
│           │     [Cancel]    [Load Anyway]       │          │
│           │                                     │          │
│           └─────────────────────────────────────┘          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 7. Error States with Recovery Options

**File Type Error Screen:**
```
┌─────────────────────────────────────────────────────────────┐
│                          MDEdit                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                             │
│    ┌─────────────────────────────────────────────────┐    │
│    │                                                 │    │
│    │              🚫 File Not Supported             │    │
│    │                                                 │    │
│    │        The file "document.docx" cannot be       │    │
│    │        opened in MDEdit.                        │    │
│    │                                                 │    │
│    │        Supported formats:                       │    │
│    │        • .md (Markdown files)                   │    │
│    │        • .txt (Plain text files)                │    │
│    │                                                 │    │
│    │        [Try Different File] [Back to Home]      │    │
│    │                                                 │    │
│    └─────────────────────────────────────────────────┘    │
│                                                             │
│          Need to convert your file? Try online converters  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```
