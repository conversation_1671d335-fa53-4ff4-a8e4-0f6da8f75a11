# Technical Assumptions

## Repository Structure: Monorepo
Single repository structure with organized component architecture, shared utilities, and clear separation between editor, preview, and file handling modules. This approach simplifies dependency management and enables efficient code sharing across the application.

## Service Architecture
**Single Page Application (SPA) with Advanced Client-Side Processing:**
- React 18+ application with concurrent features for optimal performance
- Web Workers for markdown parsing of large files (100KB+) to maintain UI responsiveness
- Progressive Web App (PWA) capabilities with service worker for offline editing
- Client-side only architecture with no backend dependencies for MVP

## Testing Requirements
**Full Testing Pyramid with User Experience Focus:**
- Unit tests for markdown parsing, file handling, and core utilities
- Integration tests for editor-preview synchronization and drag-drop workflows
- End-to-end tests covering complete user journeys from file drop to save
- Performance testing for large file handling and real-time preview updates
- Cross-browser compatibility testing across all supported browsers

## Additional Technical Assumptions and Requests

**Frontend Technology Stack:**
- **React 18+** with TypeScript for type safety and maintainable code
- **CodeMirror 6** as the advanced editor component providing syntax highlighting, keyboard shortcuts, and extensible architecture
- **Marked.js with GFM plugin** for GitHub Flavored Markdown compatibility ensuring perfect rendering match
- **Vite** as build tool for fast development and optimized production builds with code splitting

**Performance Architecture:**
- **File Size Handling:** Support up to 200KB files with hybrid parsing strategy (main thread <50KB, Web Worker ≥50KB)
- **Real-time Updates:** 100ms debounced parsing with incremental DOM updates
- **Bundle Optimization:** Code splitting by feature, tree shaking, and modern ES modules
- **Memory Management:** Virtual scrolling for large documents and efficient editor buffer management

**Browser Integration:**
- **File API** for drag-and-drop functionality with comprehensive file validation
- **Web Workers** for heavy markdown processing without blocking UI thread
- **Service Worker** for PWA capabilities and offline editing support
- **Local Storage** for user preferences and unsaved content recovery

**Development and Deployment:**
- **Static Site Hosting:** Vercel or Netlify with CDN distribution for global performance
- **CI/CD Pipeline:** GitHub Actions with automated testing, bundle size monitoring, and performance regression detection
- **Monitoring:** Performance tracking, error logging, and user analytics integration
- **Security:** Content Security Policy (CSP) headers, XSS protection, and client-side file validation

**Advanced Features Architecture:**
- **Scroll Synchronization:** Bidirectional scrolling between editor and preview with line mapping
- **Keyboard Shortcuts:** Full IDE-style shortcuts (Ctrl+Z, Ctrl+F, Ctrl+S, etc.) with customization support
- **Theme System:** Dark/light mode with system preference detection and user override
- **Export Capabilities:** Multiple format exports (HTML, PDF) using client-side conversion libraries

**Mobile and Accessibility:**
- **Progressive Web App:** Installable mobile experience with offline capability
- **WCAG AA Compliance:** Full accessibility support with screen reader compatibility
- **Responsive Architecture:** Adaptive layouts with optimal mobile editing experience
- **Touch Optimization:** Mobile-specific interactions and gesture support
