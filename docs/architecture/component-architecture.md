# Component Architecture

## Core Component Hierarchy

```mermaid
graph TD
    A[App] --> B[Layout]
    B --> C[Header]
    B --> D[MainEditor]
    B --> E[StatusBar]
    
    D --> F{Epic Check}
    F -->|Epic 1| G[BasicEditor]
    F -->|Epic 2+| H[AdvancedEditor]
    
    D --> I[PreviewPane]
    D --> J[SplitPaneController]
    
    H --> K[CodeMirrorWrapper]
    H --> L[SyntaxHighlighter]
    H --> M[SearchOverlay]
    
    I --> N[MarkdownRenderer]
    I --> O[ScrollSyncHandler]
    
    A --> P[FileDropZone]
    A --> Q[ErrorBoundary]
    A --> R[PWAManager]
    A --> S[WorkerManager]
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style H fill:#fff3e0
    style I fill:#e8f5e8
```

## Component Specifications

**App Component (Root)**
```typescript
interface AppProps {
  initialTheme?: 'light' | 'dark' | 'system';
}

interface AppState {
  currentFile: FileData | null;
  theme: ThemeMode;
  epic: 1 | 2 | 3 | 4;
  isOffline: boolean;
}

// Progressive Epic Detection
const detectAvailableEpic = (): number => {
  if (typeof Worker !== 'undefined' && 'serviceWorker' in navigator) return 4;
  if (window.CodeMirror || import.meta.env.VITE_EPIC_LEVEL >= 3) return 3;
  if (import.meta.env.VITE_EPIC_LEVEL >= 2) return 2;
  return 1;
};
```

**MainEditor Component (Epic Progression)**
```typescript
// Epic 1: Basic Editor
const BasicEditor = ({ value, onChange }: EditorProps) => (
  <textarea
    value={value}
    onChange={(e) => onChange(e.target.value)}
    className="font-mono resize-none w-full h-full p-4"
    placeholder="Start typing your markdown..."
  />
);

// Epic 2+: Advanced Editor
const AdvancedEditor = ({ value, onChange, onScroll }: AdvancedEditorProps) => {
  const extensions = useMemo(() => [
    markdown(),
    syntaxHighlighting(defaultHighlightStyle),
    lineNumbers(),
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        onChange(update.state.doc.toString());
      }
      if (update.scrolled) {
        onScroll?.(update.view);
      }
    })
  ], [onChange, onScroll]);

  return (
    <CodeMirror
      value={value}
      extensions={extensions}
      theme={theme === 'dark' ? oneDark : undefined}
    />
  );
};
```

**Preview Pane Component**
```typescript
interface PreviewPaneProps {
  content: string;
  isLoading: boolean;
  onScroll: (scrollTop: number) => void;
}

const PreviewPane = ({ content, isLoading, onScroll }: PreviewPaneProps) => {
  const [html, setHtml] = useState('');
  const syncManager = useScrollSync();
  const workerManager = useWorkerManager();
  
  useEffect(() => {
    const processContent = async () => {
      if (content.length >= 51200) {
        // Use Web Worker for large files
        const result = await workerManager.parseMarkdown(content);
        setHtml(result);
      } else {
        // Process on main thread
        const result = marked(content, { gfm: true });
        setHtml(result);
      }
    };
    
    const debounceTimer = setTimeout(processContent, 100);
    return () => clearTimeout(debounceTimer);
  }, [content, workerManager]);
  
  return (
    <div 
      className="preview-pane overflow-auto p-4"
      onScroll={(e) => onScroll(e.currentTarget.scrollTop)}
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        </div>
      ) : (
        <div 
          dangerouslySetInnerHTML={{ __html: html }}
          className="prose prose-lg max-w-none"
        />
      )}
    </div>
  );
};
```
