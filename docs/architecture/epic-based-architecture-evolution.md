# Epic-Based Architecture Evolution

## Epic 1: Foundation Architecture

**Core Components:**
- Basic HTML textarea editor
- Simple drag-and-drop file handler  
- Manual markdown parsing with Marked.js
- Basic two-pane layout (responsive)
- File download functionality

**Technical Characteristics:**
```typescript
// Epic 1 Editor Interface
interface BasicEditor {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

// Epic 1 File Handler
interface FileHandler {
  onFileLoad: (content: string, filename: string) => void;
  onError: (error: FileError) => void;
  acceptedTypes: string[];
  maxSize: number;
}
```

**Architecture Focus:**
- Prove core value proposition quickly
- Establish robust file handling foundation
- Implement responsive layout patterns
- Build essential error handling

## Epic 2: Professional Editor Architecture

**Enhanced Components:**
- CodeMirror 6 editor with markdown syntax highlighting
- Real-time preview synchronization system
- GitHub Flavored Markdown rendering
- Scroll synchronization between panes
- Performance optimization for larger files

**CodeMirror Integration Architecture:**
```typescript
// CodeMirror Editor Wrapper
interface AdvancedEditor {
  value: string;
  onChange: (value: string) => void;
  extensions: Extension[];
  theme: 'light' | 'dark';
  onScroll?: (scrollInfo: ScrollInfo) => void;
}

// Required CodeMirror Extensions
const editorExtensions = [
  markdown(),
  syntaxHighlighting(defaultHighlightStyle),
  lineNumbers(),
  foldGutter(),
  bracketMatching(),
  searchKeymap,
  historyKeymap,
  defaultKeymap
];
```

**Real-Time Synchronization System:**
```typescript
// Debounced Update Manager
class PreviewSyncManager {
  private debounceTimer: NodeJS.Timeout | null = null;
  private readonly DEBOUNCE_DELAY = 100; // ms
  
  scheduleUpdate(content: string, callback: (html: string) => void) {
    if (this.debounceTimer) clearTimeout(this.debounceTimer);
    
    this.debounceTimer = setTimeout(() => {
      this.processContent(content, callback);
    }, this.DEBOUNCE_DELAY);
  }
  
  private processContent(content: string, callback: (html: string) => void) {
    if (content.length >= 51200) { // 50KB threshold
      this.processWithWorker(content, callback);
    } else {
      this.processOnMainThread(content, callback);
    }
  }
}
```

**Architecture Risks Addressed:**
- **Scroll Synchronization:** Implemented line-to-element mapping with fallback strategies
- **CodeMirror Performance:** Lazy loading and extension management for optimal bundle size
- **Real-time Updates:** Smart debouncing prevents excessive rendering

## Epic 3: Web Worker Implementation Architecture

**Web Worker Strategy for Large Files:**

The Web Worker implementation addresses the critical performance requirement of handling files up to 200KB without blocking the UI thread. Files ≥50KB are automatically processed in background workers.

**Worker Communication Protocol:**
```typescript
// Main Thread to Worker Messages
interface WorkerMessage {
  type: 'parse' | 'cancel';
  id: string;
  payload: {
    content: string;
    options: ParseOptions;
  };
}

// Worker to Main Thread Response
interface WorkerResponse {
  type: 'parsed' | 'error' | 'progress';
  id: string;
  payload: {
    html?: string;
    error?: string;
    progress?: number;
  };
}
```

**Worker Implementation:**
```typescript
// markdown-worker.ts
import { marked } from 'marked';
import { gfm } from 'marked-gfm';

// Configure marked for GFM compatibility
marked.use(gfm());

self.onmessage = (event: MessageEvent<WorkerMessage>) => {
  const { type, id, payload } = event.data;
  
  if (type === 'parse') {
    try {
      // Process large content in chunks for progress reporting
      const chunks = chunkContent(payload.content, 10000); // 10KB chunks
      let html = '';
      
      chunks.forEach((chunk, index) => {
        html += marked(chunk, payload.options);
        
        // Report progress for very large files
        if (chunks.length > 5) {
          self.postMessage({
            type: 'progress',
            id,
            payload: { progress: (index + 1) / chunks.length }
          });
        }
      });
      
      self.postMessage({
        type: 'parsed',
        id,
        payload: { html }
      });
    } catch (error) {
      self.postMessage({
        type: 'error',
        id,
        payload: { error: error.message }
      });
    }
  }
};

function chunkContent(content: string, chunkSize: number): string[] {
  const chunks = [];
  for (let i = 0; i < content.length; i += chunkSize) {
    chunks.push(content.slice(i, i + chunkSize));
  }
  return chunks;
}
```

**Worker Manager:**
```typescript
class WorkerManager {
  private worker: Worker | null = null;
  private pendingRequests = new Map<string, WorkerPromise>();
  
  constructor() {
    this.initializeWorker();
  }
  
  async parseMarkdown(content: string, options: ParseOptions): Promise<string> {
    if (!this.worker) throw new Error('Worker not available');
    
    const id = crypto.randomUUID();
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      
      this.worker!.postMessage({
        type: 'parse',
        id,
        payload: { content, options }
      });
      
      // Timeout for very large files (30 seconds)
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('Worker timeout'));
        }
      }, 30000);
    });
  }
  
  private initializeWorker() {
    this.worker = new Worker(
      new URL('../workers/markdown-worker.ts', import.meta.url),
      { type: 'module' }
    );
    
    this.worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
      const { type, id, payload } = event.data;
      const request = this.pendingRequests.get(id);
      
      if (!request) return;
      
      switch (type) {
        case 'parsed':
          this.pendingRequests.delete(id);
          request.resolve(payload.html!);
          break;
        case 'error':
          this.pendingRequests.delete(id);
          request.reject(new Error(payload.error));
          break;
        case 'progress':
          // Could emit progress events here
          break;
      }
    };
  }
}
```

**Performance Characteristics:**
- Files < 50KB: Main thread processing (~5-15ms)
- Files 50-200KB: Web Worker processing (~20-100ms) 
- UI remains responsive during all operations
- Automatic fallback to main thread if Worker fails
