# MDEdit Fullstack Architecture Document

## Table of Contents

- [MDEdit Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Epic-Based Architecture Evolution](./epic-based-architecture-evolution.md)
    - [Epic 1: Foundation Architecture](./epic-based-architecture-evolution.md#epic-1-foundation-architecture)
    - [Epic 2: Professional Editor Architecture](./epic-based-architecture-evolution.md#epic-2-professional-editor-architecture)
    - [Epic 3: Web Worker Implementation Architecture](./epic-based-architecture-evolution.md#epic-3-web-worker-implementation-architecture)
  - [Scroll Synchronization Technical Risk Mitigation](./scroll-synchronization-technical-risk-mitigation.md)
    - [Line-to-Element Mapping System](./scroll-synchronization-technical-risk-mitigation.md#line-to-element-mapping-system)
    - [Epic 4: PWA Architecture](./scroll-synchronization-technical-risk-mitigation.md#epic-4-pwa-architecture)
  - [Cross-Browser Drag-and-Drop Consistency](./cross-browser-drag-and-drop-consistency.md)
  - [Component Architecture](./component-architecture.md)
    - [Core Component Hierarchy](./component-architecture.md#core-component-hierarchy)
    - [Component Specifications](./component-architecture.md#component-specifications)
  - [Project Structure](./project-structure.md)
  - [Performance and Deployment Strategy](./performance-and-deployment-strategy.md)
    - [Bundle Optimization Strategy](./performance-and-deployment-strategy.md#bundle-optimization-strategy)
    - [Deployment Configuration](./performance-and-deployment-strategy.md#deployment-configuration)
  - [Technical Risk Assessment Summary](./technical-risk-assessment-summary.md)
    - [Identified Risks and Mitigation Strategies](./technical-risk-assessment-summary.md#identified-risks-and-mitigation-strategies)
