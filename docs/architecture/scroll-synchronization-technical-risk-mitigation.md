# Scroll Synchronization Technical Risk Mitigation

**Problem:** Maintaining accurate scroll position synchronization between CodeMirror editor and HTML preview panes presents significant technical challenges due to:
- Variable line heights in rendered HTML
- Complex markdown structures (tables, code blocks, lists)
- Dynamic content rendering timing
- Cross-browser viewport differences

**Solution Architecture:**

## Line-to-Element Mapping System

```typescript
// Scroll Synchronization Manager
class ScrollSyncManager {
  private editorToPreviewMap = new Map<number, number>();
  private previewToEditorMap = new Map<number, number>();
  private isUserScrolling = false;
  private scrollTimeout: NodeJS.Timeout | null = null;
  
  constructor(
    private editor: EditorView,
    private previewElement: HTMLElement
  ) {
    this.setupScrollListeners();
  }
  
  // Create mapping between editor lines and preview elements
  createScrollMap(editorContent: string, previewHTML: string) {
    const lines = editorContent.split('\n');
    const previewElements = this.getPreviewElements();
    
    let currentLine = 0;
    let currentElement = 0;
    
    // Parse markdown structure to create line mappings
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Headers create reliable anchor points
      if (line.match(/^#{1,6}\s/)) {
        const headerLevel = line.match(/^(#{1,6})/)?.[1].length || 1;
        const element = this.findHeaderElement(previewElements, headerLevel, i);
        if (element) {
          this.mapLineToElement(i, element);
          currentElement = element;
        }
      }
      
      // Code blocks are reliable markers
      else if (line.match(/^```/)) {
        const codeElement = this.findCodeBlock(previewElements, currentElement);
        if (codeElement) {
          this.mapLineToElement(i, codeElement);
        }
      }
      
      // Tables have distinct structures
      else if (line.includes('|')) {
        const tableElement = this.findTableElement(previewElements, currentElement);
        if (tableElement) {
          this.mapLineToElement(i, tableElement);
        }
      }
      
      // Fill gaps with interpolation
      else {
        this.interpolateMapping(i);
      }
    }
  }
  
  // Bidirectional scroll synchronization
  syncEditorToPreview(editorLine: number) {
    if (this.isUserScrolling) return;
    
    const targetElement = this.findNearestPreviewElement(editorLine);
    if (!targetElement) return;
    
    const elementTop = targetElement.offsetTop;
    const containerHeight = this.previewElement.clientHeight;
    const scrollTop = Math.max(0, elementTop - containerHeight / 3);
    
    this.isUserScrolling = true;
    this.previewElement.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
    
    this.resetScrollingFlag();
  }
  
  syncPreviewToEditor(previewScrollTop: number) {
    if (this.isUserScrolling) return;
    
    const targetLine = this.findNearestEditorLine(previewScrollTop);
    if (targetLine === null) return;
    
    this.isUserScrolling = true;
    this.editor.dispatch({
      effects: EditorView.scrollIntoView(
        this.editor.state.doc.line(targetLine + 1).from,
        { y: 'center' }
      )
    });
    
    this.resetScrollingFlag();
  }
  
  // Fallback strategies for difficult cases
  private findNearestPreviewElement(line: number): HTMLElement | null {
    // Try exact mapping first
    if (this.editorToPreviewMap.has(line)) {
      return this.getElementByIndex(this.editorToPreviewMap.get(line)!);
    }
    
    // Find nearest mapped line
    let nearestLine = line;
    let searchRadius = 1;
    
    while (searchRadius < 50) { // Reasonable search limit
      const above = line - searchRadius;
      const below = line + searchRadius;
      
      if (this.editorToPreviewMap.has(above)) {
        nearestLine = above;
        break;
      }
      
      if (this.editorToPreviewMap.has(below)) {
        nearestLine = below;
        break;
      }
      
      searchRadius++;
    }
    
    return this.getElementByIndex(this.editorToPreviewMap.get(nearestLine)!);
  }
  
  // Handle user-initiated scrolling conflicts
  private setupScrollListeners() {
    // Editor scroll handler
    this.editor.scrollDOM.addEventListener('scroll', () => {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);
      
      this.scrollTimeout = setTimeout(() => {
        const { from } = this.editor.viewport;
        const line = this.editor.state.doc.lineAt(from).number - 1;
        this.syncEditorToPreview(line);
      }, 100);
    });
    
    // Preview scroll handler
    this.previewElement.addEventListener('scroll', () => {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);
      
      this.scrollTimeout = setTimeout(() => {
        this.syncPreviewToEditor(this.previewElement.scrollTop);
      }, 100);
    });
  }
  
  private resetScrollingFlag() {
    setTimeout(() => {
      this.isUserScrolling = false;
    }, 300);
  }
}
```

**Key Architectural Decisions:**
- **Anchor Point Strategy:** Headers, code blocks, and tables serve as reliable synchronization anchors
- **Interpolation Fallback:** Gaps between anchors filled with proportional positioning
- **User Intent Preservation:** Scroll conflict resolution prioritizes user-initiated scrolling
- **Performance Optimization:** 100ms debouncing prevents excessive synchronization calls
- **Graceful Degradation:** System remains functional even with imperfect mappings

## Epic 4: PWA Architecture

**Progressive Web App Implementation:**

```typescript
// PWA Service Worker Strategy
interface PWAConfig {
  cacheStrategy: 'networkFirst' | 'cacheFirst';
  offlinePages: string[];
  staticAssets: string[];
  dynamicCaching: boolean;
}

// Service Worker Registration
class PWAManager {
  private registration: ServiceWorkerRegistration | null = null;
  
  async initialize() {
    if ('serviceWorker' in navigator) {
      try {
        this.registration = await navigator.serviceWorker.register(
          '/sw.js',
          { scope: '/' }
        );
        
        this.setupUpdateHandling();
        this.setupOfflineHandling();
      } catch (error) {
        console.warn('PWA not available:', error);
      }
    }
  }
  
  private setupUpdateHandling() {
    this.registration?.addEventListener('updatefound', () => {
      const newWorker = this.registration!.installing;
      
      newWorker?.addEventListener('statechange', () => {
        if (newWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            // Show update notification
            this.showUpdateAvailable();
          }
        }
      });
    });
  }
  
  // Offline file storage using IndexedDB
  async storeFileOffline(filename: string, content: string) {
    const db = await this.openOfflineDB();
    const transaction = db.transaction(['files'], 'readwrite');
    const store = transaction.objectStore('files');
    
    await store.put({
      id: filename,
      content,
      timestamp: Date.now(),
      synced: false
    });
  }
  
  async getOfflineFiles(): Promise<OfflineFile[]> {
    const db = await this.openOfflineDB();
    const transaction = db.transaction(['files'], 'readonly');
    const store = transaction.objectStore('files');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}
```

**Offline Capabilities:**
- Complete application caching for offline usage
- IndexedDB storage for user content persistence
- Background sync for future cloud storage integration
- Offline-first editing with sync indicators
