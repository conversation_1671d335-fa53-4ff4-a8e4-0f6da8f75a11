# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.0+ | Type-safe JavaScript development | Prevents runtime errors, improves maintainability, required for complex editor logic |
| Frontend Framework | React | 18+ | Component-based UI framework | Concurrent features for optimal performance, extensive ecosystem, team familiarity |
| Build Tool | Vite | 4.0+ | Fast development and build tool | Sub-second HMR, optimized production builds, excellent TypeScript support |
| Editor Component | CodeMirror | 6.0+ | Advanced code editor | Professional editing features, syntax highlighting, extensible architecture for markdown |
| Markdown Parser | Marked.js + GFM | 9.0+ | GitHub Flavored Markdown parsing | Perfect GitHub compatibility, extensible, well-maintained |
| UI Component Library | Custom + Headless UI | 1.7+ | Accessible UI primitives | WCAG AA compliance, unstyled components for custom design |
| State Management | Zustand | 4.4+ | Lightweight state management | Simple API, TypeScript-first, minimal boilerplate for SPA |
| CSS Framework | Tailwind CSS | 3.3+ | Utility-first CSS framework | Rapid development, consistent design tokens, tree-shaking |
| PWA Framework | Vite PWA Plugin | 0.16+ | Progressive Web App capabilities | Service worker generation, offline caching, installability |
| File Processing | Web Workers API | Native | Background processing for large files | Prevents UI blocking during heavy markdown parsing |
| Testing Framework | Vitest | 0.34+ | Fast unit testing | Vite-native, Jest-compatible API, excellent TypeScript support |
| E2E Testing | Playwright | 1.40+ | Cross-browser end-to-end testing | Multi-browser testing, reliable drag-and-drop testing |
| Linting | ESLint + Prettier | Latest | Code quality and formatting | Consistent code style, catches errors early |
| CI/CD | GitHub Actions | N/A | Automated testing and deployment | Free for open source, excellent GitHub integration |
| Deployment | Vercel | N/A | Static site hosting with CDN | Optimal React/Vite integration, global CDN, preview deployments |
| Monitoring | Sentry | 7.0+ | Error tracking and performance | Real-time error monitoring, performance insights |
