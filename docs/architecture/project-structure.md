# Project Structure

```
mdedit/
├── .github/
│   └── workflows/
│       ├── ci.yml                  # Test and build validation
│       └── deploy.yml              # Deploy to Vercel
├── public/
│   ├── manifest.json               # PWA manifest
│   ├── sw.js                       # Service worker
│   └── icons/                      # PWA icons
├── src/
│   ├── components/
│   │   ├── ui/                     # Base UI components
│   │   │   ├── Button.tsx
│   │   │   ├── Modal.tsx
│   │   │   └── Spinner.tsx
│   │   ├── editor/                 # Editor components
│   │   │   ├── BasicEditor.tsx     # Epic 1 editor
│   │   │   ├── AdvancedEditor.tsx  # Epic 2+ editor
│   │   │   ├── CodeMirrorWrapper.tsx
│   │   │   └── SearchOverlay.tsx
│   │   ├── preview/                # Preview components
│   │   │   ├── PreviewPane.tsx
│   │   │   ├── MarkdownRenderer.tsx
│   │   │   └── ScrollSyncHandler.tsx
│   │   ├── layout/                 # Layout components
│   │   │   ├── Header.tsx
│   │   │   ├── MainEditor.tsx
│   │   │   ├── SplitPane.tsx
│   │   │   └── StatusBar.tsx
│   │   └── file/                   # File handling
│   │       ├── FileDropZone.tsx
│   │       ├── DropHandler.tsx
│   │       └── FileValidator.tsx
│   ├── hooks/                      # Custom React hooks
│   │   ├── useFileHandler.ts
│   │   ├── useScrollSync.ts
│   │   ├── useWorkerManager.ts
│   │   ├── usePWA.ts
│   │   └── useEpicDetection.ts
│   ├── services/                   # Business logic services
│   │   ├── MarkdownParser.ts
│   │   ├── WorkerManager.ts
│   │   ├── ScrollSyncManager.ts
│   │   ├── PWAManager.ts
│   │   └── StorageManager.ts
│   ├── workers/                    # Web Workers
│   │   └── markdown-worker.ts
│   ├── types/                      # TypeScript definitions
│   │   ├── editor.ts
│   │   ├── file.ts
│   │   └── worker.ts
│   ├── utils/                      # Utility functions
│   │   ├── file-utils.ts
│   │   ├── markdown-utils.ts
│   │   └── performance.ts
│   ├── styles/                     # Global styles
│   │   ├── globals.css
│   │   ├── components.css
│   │   └── themes.css
│   ├── App.tsx                     # Root component
│   ├── main.tsx                    # Entry point
│   └── vite-env.d.ts               # Vite types
├── tests/
│   ├── unit/                       # Unit tests
│   ├── integration/                # Integration tests
│   └── e2e/                        # Playwright E2E tests
├── docs/
│   ├── prd.md                      # Product requirements
│   ├── front-end-spec.md          # UI/UX specification
│   └── architecture.md            # This document
├── .env.example                    # Environment template
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.js
├── eslint.config.js
├── playwright.config.ts
└── README.md
```
