# Introduction

This document outlines the complete technical architecture for MDEdit, a React TypeScript markdown editor focused on drag-and-drop file workflows and real-time preview synchronization. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This architecture addresses the specific requirements identified in the PRD, including CodeMirror 6 integration, real-time preview synchronization, Web Worker implementation for large files, PWA capabilities, and the four-epic progression model that enables incremental feature delivery.

## Starter Template or Existing Project

N/A - Greenfield project. We will use Vite with React TypeScript template as the foundation, enhanced with our specific requirements.

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial architecture document creation | <PERSON> (Architect) |
