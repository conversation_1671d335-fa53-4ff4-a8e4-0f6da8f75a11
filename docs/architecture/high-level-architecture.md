# High Level Architecture

## Technical Summary

MDEdit is architected as a client-side Single Page Application (SPA) built with React 18+ and TypeScript, utilizing Vite for optimal development experience and production builds. The architecture employs a progressive enhancement strategy across four epics, starting with basic textarea functionality and evolving to advanced CodeMirror 6 editor capabilities with real-time preview synchronization. Web Workers handle markdown parsing for files ≥50KB to maintain UI responsiveness, while the application operates entirely client-side with no backend dependencies for the MVP. The system implements PWA capabilities for offline editing and cross-device reliability, with careful attention to cross-browser drag-and-drop consistency and scroll synchronization challenges.

## Platform and Infrastructure Choice

**Platform:** Static Site Hosting (Vercel/Netlify)  
**Key Services:** Vercel Edge Network, GitHub Actions CI/CD, Web APIs (File API, Web Workers, Service Workers)  
**Deployment Host and Regions:** Global CDN distribution via Vercel Edge Network

**Rationale:** Since MDEdit is a client-side only application with no backend requirements, static hosting provides optimal performance, cost-effectiveness, and global distribution. Vercel offers excellent integration with React/Vite projects and provides the CDN distribution needed for sub-2-second load times globally.

## Repository Structure

**Structure:** Monorepo with organized component architecture  
**Monorepo Tool:** npm workspaces (simpler setup for client-only project)  
**Package Organization:** Feature-based packages with shared utilities

## High Level Architecture Diagram

```mermaid
graph TD
    A[User Browser] --> B[Vercel CDN]
    B --> C[React SPA]
    C --> D[File Drag & Drop Handler]
    C --> E[Editor Component]
    C --> F[Preview Component]
    C --> G[Web Worker Manager]
    
    D --> H[File API]
    E --> I{File Size Check}
    I -->|< 50KB| J[Main Thread Parser]
    I -->|≥ 50KB| K[Web Worker Parser]
    J --> L[Markdown Renderer]
    K --> L
    L --> F
    
    C --> M[Service Worker]
    M --> N[PWA Cache]
    M --> O[Offline Storage]
    
    P[GitHub Actions] --> Q[Build Pipeline]
    Q --> B
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style G fill:#fff3e0
    style M fill:#e8f5e8
```

## Architectural Patterns

- **Progressive Web App (PWA):** Installable application with offline capabilities and native-like experience - _Rationale:_ Enables cross-device usage and offline editing as required in Epic 4
- **Component-Based UI:** Modular React components with TypeScript interfaces - _Rationale:_ Maintainability and type safety for complex editor functionality
- **Web Worker Pattern:** Background processing for heavy computational tasks - _Rationale:_ Maintains UI responsiveness when processing large markdown files
- **Progressive Enhancement:** Feature layering from basic to advanced functionality - _Rationale:_ Enables incremental delivery across four epics while maintaining user value
- **Client-Side File Processing:** No server dependencies for core functionality - _Rationale:_ Privacy, performance, and simplified architecture as specified in PRD
- **Real-Time Observer Pattern:** Live synchronization between editor and preview - _Rationale:_ Sub-100ms updates required for optimal user experience
