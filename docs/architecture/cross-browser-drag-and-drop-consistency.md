# Cross-Browser Drag-and-Drop Consistency

**Problem:** Drag-and-drop behavior varies significantly across browsers, particularly:
- <PERSON><PERSON>'s restrictive file access policies
- Firefox's different drag event handling
- Mobile browser limitations
- Inconsistent drag feedback states

**Solution Architecture:**

```typescript
// Cross-Browser Drag-and-Drop Handler
class UniversalDropHandler {
  private dragCounter = 0;
  private supportedTypes = ['text/markdown', 'text/plain'];
  
  constructor(private dropZone: HTMLElement) {
    this.setupEventListeners();
  }
  
  private setupEventListeners() {
    // Prevent default browser behavior
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.preventDefaults);
      document.body.addEventListener(eventName, this.preventDefaults);
    });
    
    // Handle drag enter/leave with counter for nested elements
    this.dropZone.addEventListener('dragenter', this.handleDragEnter.bind(this));
    this.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
    this.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
    this.dropZone.addEventListener('drop', this.handleDrop.bind(this));
  }
  
  private preventDefaults(e: Event) {
    e.preventDefault();
    e.stopPropagation();
  }
  
  private handleDragEnter(e: DragEvent) {
    this.dragCounter++;
    
    if (this.validateDraggedItems(e.dataTransfer)) {
      this.dropZone.classList.add('drag-valid');
      this.showDropFeedback(true);
    } else {
      this.dropZone.classList.add('drag-invalid');
      this.showDropFeedback(false);
    }
  }
  
  private handleDragLeave() {
    this.dragCounter--;
    
    if (this.dragCounter === 0) {
      this.dropZone.classList.remove('drag-valid', 'drag-invalid');
      this.hideDropFeedback();
    }
  }
  
  private async handleDrop(e: DragEvent) {
    this.dragCounter = 0;
    this.dropZone.classList.remove('drag-valid', 'drag-invalid');
    this.hideDropFeedback();
    
    const files = Array.from(e.dataTransfer?.files || []);
    
    if (files.length === 0) {
      this.handleTextDrop(e.dataTransfer?.getData('text/plain') || '');
      return;
    }
    
    // Process first file only (MVP limitation)
    const file = files[0];
    
    try {
      const content = await this.readFile(file);
      this.onFileLoaded(content, file.name);
    } catch (error) {
      this.onError(new Error(`Failed to read file: ${error.message}`));
    }
  }
  
  // Cross-browser file reading
  private readFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      // Validate file type and size
      if (!this.isValidFile(file)) {
        reject(new Error(`Unsupported file type: ${file.type}`));
        return;
      }
      
      if (file.size > 209715200) { // 200MB limit
        reject(new Error('File too large (max 200MB)'));
        return;
      }
      
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const result = event.target?.result;
        if (typeof result === 'string') {
          resolve(result);
        } else {
          reject(new Error('Failed to read file as text'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('File reading failed'));
      };
      
      // Handle different encoding detection
      reader.readAsText(file, 'UTF-8');
    });
  }
  
  // Browser-specific validation
  private validateDraggedItems(dataTransfer: DataTransfer | null): boolean {
    if (!dataTransfer) return false;
    
    // Check for files
    if (dataTransfer.files.length > 0) {
      return Array.from(dataTransfer.files).some(file => this.isValidFile(file));
    }
    
    // Check for dragged text content
    const types = dataTransfer.types;
    return types.includes('text/plain') || types.includes('text/markdown');
  }
  
  private isValidFile(file: File): boolean {
    const validTypes = [
      'text/markdown',
      'text/plain',
      'application/octet-stream' // Fallback for .md files
    ];
    
    const validExtensions = ['.md', '.txt', '.markdown'];
    
    return validTypes.includes(file.type) || 
           validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  }
}
```

**Cross-Browser Compatibility Features:**
- **Unified Event Handling:** Consistent drag/drop behavior across all browsers
- **Fallback Mechanisms:** Text drag support when file APIs are limited
- **Mobile Adaptation:** Touch-friendly file selection fallback for mobile devices
- **Progressive Enhancement:** Works without JavaScript, enhanced with JavaScript
