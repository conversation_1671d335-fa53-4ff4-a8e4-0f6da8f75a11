# Performance and Deployment Strategy

## Bundle Optimization Strategy

**Epic 1 Bundle (~45KB gzipped):**
- Core React + DOM handling
- Basic markdown parsing
- Essential UI components
- Basic drag-and-drop

**Epic 2 Enhancements (Lazy loaded ~85KB):**
- CodeMirror 6 editor
- Advanced syntax highlighting
- Real-time sync managers
- Scroll synchronization

**Epic 3+ Features (On-demand):**
- Web Worker implementations
- Advanced export capabilities
- Theme system enhancements
- PWA service worker

**Performance Monitoring:**
```typescript
// Performance tracking for key metrics
class PerformanceTracker {
  static trackFileProcessing(fileSize: number, processingTime: number) {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'file_processing_time', {
        custom_map: { metric1: 'file_size', metric2: 'processing_time' },
        metric1: fileSize,
        metric2: processingTime
      });
    }
  }
  
  static trackPreviewLatency(updateTime: number) {
    if (updateTime > 100) { // Above target threshold
      console.warn(`Preview update exceeded 100ms: ${updateTime}ms`);
    }
  }
}
```

## Deployment Configuration

**Vite Production Build:**
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          }
        ]
      }
    })
  ],
  build: {
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          codemirror: ['@codemirror/state', '@codemirror/view', '@codemirror/lang-markdown'],
          markdown: ['marked', 'marked-gfm']
        }
      }
    }
  }
});
```

**Vercel Configuration:**
```json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm install",
  "devCommand": "npm run dev",
  "headers": [
    {
      "source": "/service-worker.js",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=0, must-revalidate"
        }
      ]
    }
  ]
}
```
