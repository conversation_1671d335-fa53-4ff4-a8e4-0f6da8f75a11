# MDEdit Fullstack Architecture Document

## Introduction

This document outlines the complete technical architecture for MDEdit, a React TypeScript markdown editor focused on drag-and-drop file workflows and real-time preview synchronization. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This architecture addresses the specific requirements identified in the PRD, including CodeMirror 6 integration, real-time preview synchronization, Web Worker implementation for large files, PWA capabilities, and the four-epic progression model that enables incremental feature delivery.

### Starter Template or Existing Project

N/A - Greenfield project. We will use Vite with React TypeScript template as the foundation, enhanced with our specific requirements.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-01 | 1.0 | Initial architecture document creation | Winston (Architect) |

## High Level Architecture

### Technical Summary

MDEdit is architected as a client-side Single Page Application (SPA) built with React 18+ and TypeScript, utilizing Vite for optimal development experience and production builds. The architecture employs a progressive enhancement strategy across four epics, starting with basic textarea functionality and evolving to advanced CodeMirror 6 editor capabilities with real-time preview synchronization. Web Workers handle markdown parsing for files ≥50KB to maintain UI responsiveness, while the application operates entirely client-side with no backend dependencies for the MVP. The system implements PWA capabilities for offline editing and cross-device reliability, with careful attention to cross-browser drag-and-drop consistency and scroll synchronization challenges.

### Platform and Infrastructure Choice

**Platform:** Static Site Hosting (Vercel/Netlify)  
**Key Services:** Vercel Edge Network, GitHub Actions CI/CD, Web APIs (File API, Web Workers, Service Workers)  
**Deployment Host and Regions:** Global CDN distribution via Vercel Edge Network

**Rationale:** Since MDEdit is a client-side only application with no backend requirements, static hosting provides optimal performance, cost-effectiveness, and global distribution. Vercel offers excellent integration with React/Vite projects and provides the CDN distribution needed for sub-2-second load times globally.

### Repository Structure

**Structure:** Monorepo with organized component architecture  
**Monorepo Tool:** npm workspaces (simpler setup for client-only project)  
**Package Organization:** Feature-based packages with shared utilities

### High Level Architecture Diagram

```mermaid
graph TD
    A[User Browser] --> B[Vercel CDN]
    B --> C[React SPA]
    C --> D[File Drag & Drop Handler]
    C --> E[Editor Component]
    C --> F[Preview Component]
    C --> G[Web Worker Manager]
    
    D --> H[File API]
    E --> I{File Size Check}
    I -->|< 50KB| J[Main Thread Parser]
    I -->|≥ 50KB| K[Web Worker Parser]
    J --> L[Markdown Renderer]
    K --> L
    L --> F
    
    C --> M[Service Worker]
    M --> N[PWA Cache]
    M --> O[Offline Storage]
    
    P[GitHub Actions] --> Q[Build Pipeline]
    Q --> B
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style G fill:#fff3e0
    style M fill:#e8f5e8
```

### Architectural Patterns

- **Progressive Web App (PWA):** Installable application with offline capabilities and native-like experience - _Rationale:_ Enables cross-device usage and offline editing as required in Epic 4
- **Component-Based UI:** Modular React components with TypeScript interfaces - _Rationale:_ Maintainability and type safety for complex editor functionality
- **Web Worker Pattern:** Background processing for heavy computational tasks - _Rationale:_ Maintains UI responsiveness when processing large markdown files
- **Progressive Enhancement:** Feature layering from basic to advanced functionality - _Rationale:_ Enables incremental delivery across four epics while maintaining user value
- **Client-Side File Processing:** No server dependencies for core functionality - _Rationale:_ Privacy, performance, and simplified architecture as specified in PRD
- **Real-Time Observer Pattern:** Live synchronization between editor and preview - _Rationale:_ Sub-100ms updates required for optimal user experience

## Tech Stack

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.0+ | Type-safe JavaScript development | Prevents runtime errors, improves maintainability, required for complex editor logic |
| Frontend Framework | React | 18+ | Component-based UI framework | Concurrent features for optimal performance, extensive ecosystem, team familiarity |
| Build Tool | Vite | 4.0+ | Fast development and build tool | Sub-second HMR, optimized production builds, excellent TypeScript support |
| Editor Component | CodeMirror | 6.0+ | Advanced code editor | Professional editing features, syntax highlighting, extensible architecture for markdown |
| Markdown Parser | Marked.js + GFM | 9.0+ | GitHub Flavored Markdown parsing | Perfect GitHub compatibility, extensible, well-maintained |
| UI Component Library | Custom + Headless UI | 1.7+ | Accessible UI primitives | WCAG AA compliance, unstyled components for custom design |
| State Management | Zustand | 4.4+ | Lightweight state management | Simple API, TypeScript-first, minimal boilerplate for SPA |
| CSS Framework | Tailwind CSS | 3.3+ | Utility-first CSS framework | Rapid development, consistent design tokens, tree-shaking |
| PWA Framework | Vite PWA Plugin | 0.16+ | Progressive Web App capabilities | Service worker generation, offline caching, installability |
| File Processing | Web Workers API | Native | Background processing for large files | Prevents UI blocking during heavy markdown parsing |
| Testing Framework | Vitest | 0.34+ | Fast unit testing | Vite-native, Jest-compatible API, excellent TypeScript support |
| E2E Testing | Playwright | 1.40+ | Cross-browser end-to-end testing | Multi-browser testing, reliable drag-and-drop testing |
| Linting | ESLint + Prettier | Latest | Code quality and formatting | Consistent code style, catches errors early |
| CI/CD | GitHub Actions | N/A | Automated testing and deployment | Free for open source, excellent GitHub integration |
| Deployment | Vercel | N/A | Static site hosting with CDN | Optimal React/Vite integration, global CDN, preview deployments |
| Monitoring | Sentry | 7.0+ | Error tracking and performance | Real-time error monitoring, performance insights |

## Epic-Based Architecture Evolution

### Epic 1: Foundation Architecture

**Core Components:**
- Basic HTML textarea editor
- Simple drag-and-drop file handler  
- Manual markdown parsing with Marked.js
- Basic two-pane layout (responsive)
- File download functionality

**Technical Characteristics:**
```typescript
// Epic 1 Editor Interface
interface BasicEditor {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

// Epic 1 File Handler
interface FileHandler {
  onFileLoad: (content: string, filename: string) => void;
  onError: (error: FileError) => void;
  acceptedTypes: string[];
  maxSize: number;
}
```

**Architecture Focus:**
- Prove core value proposition quickly
- Establish robust file handling foundation
- Implement responsive layout patterns
- Build essential error handling

### Epic 2: Professional Editor Architecture

**Enhanced Components:**
- CodeMirror 6 editor with markdown syntax highlighting
- Real-time preview synchronization system
- GitHub Flavored Markdown rendering
- Scroll synchronization between panes
- Performance optimization for larger files

**CodeMirror Integration Architecture:**
```typescript
// CodeMirror Editor Wrapper
interface AdvancedEditor {
  value: string;
  onChange: (value: string) => void;
  extensions: Extension[];
  theme: 'light' | 'dark';
  onScroll?: (scrollInfo: ScrollInfo) => void;
}

// Required CodeMirror Extensions
const editorExtensions = [
  markdown(),
  syntaxHighlighting(defaultHighlightStyle),
  lineNumbers(),
  foldGutter(),
  bracketMatching(),
  searchKeymap,
  historyKeymap,
  defaultKeymap
];
```

**Real-Time Synchronization System:**
```typescript
// Debounced Update Manager
class PreviewSyncManager {
  private debounceTimer: NodeJS.Timeout | null = null;
  private readonly DEBOUNCE_DELAY = 100; // ms
  
  scheduleUpdate(content: string, callback: (html: string) => void) {
    if (this.debounceTimer) clearTimeout(this.debounceTimer);
    
    this.debounceTimer = setTimeout(() => {
      this.processContent(content, callback);
    }, this.DEBOUNCE_DELAY);
  }
  
  private processContent(content: string, callback: (html: string) => void) {
    if (content.length >= 51200) { // 50KB threshold
      this.processWithWorker(content, callback);
    } else {
      this.processOnMainThread(content, callback);
    }
  }
}
```

**Architecture Risks Addressed:**
- **Scroll Synchronization:** Implemented line-to-element mapping with fallback strategies
- **CodeMirror Performance:** Lazy loading and extension management for optimal bundle size
- **Real-time Updates:** Smart debouncing prevents excessive rendering

### Epic 3: Web Worker Implementation Architecture

**Web Worker Strategy for Large Files:**

The Web Worker implementation addresses the critical performance requirement of handling files up to 200KB without blocking the UI thread. Files ≥50KB are automatically processed in background workers.

**Worker Communication Protocol:**
```typescript
// Main Thread to Worker Messages
interface WorkerMessage {
  type: 'parse' | 'cancel';
  id: string;
  payload: {
    content: string;
    options: ParseOptions;
  };
}

// Worker to Main Thread Response
interface WorkerResponse {
  type: 'parsed' | 'error' | 'progress';
  id: string;
  payload: {
    html?: string;
    error?: string;
    progress?: number;
  };
}
```

**Worker Implementation:**
```typescript
// markdown-worker.ts
import { marked } from 'marked';
import { gfm } from 'marked-gfm';

// Configure marked for GFM compatibility
marked.use(gfm());

self.onmessage = (event: MessageEvent<WorkerMessage>) => {
  const { type, id, payload } = event.data;
  
  if (type === 'parse') {
    try {
      // Process large content in chunks for progress reporting
      const chunks = chunkContent(payload.content, 10000); // 10KB chunks
      let html = '';
      
      chunks.forEach((chunk, index) => {
        html += marked(chunk, payload.options);
        
        // Report progress for very large files
        if (chunks.length > 5) {
          self.postMessage({
            type: 'progress',
            id,
            payload: { progress: (index + 1) / chunks.length }
          });
        }
      });
      
      self.postMessage({
        type: 'parsed',
        id,
        payload: { html }
      });
    } catch (error) {
      self.postMessage({
        type: 'error',
        id,
        payload: { error: error.message }
      });
    }
  }
};

function chunkContent(content: string, chunkSize: number): string[] {
  const chunks = [];
  for (let i = 0; i < content.length; i += chunkSize) {
    chunks.push(content.slice(i, i + chunkSize));
  }
  return chunks;
}
```

**Worker Manager:**
```typescript
class WorkerManager {
  private worker: Worker | null = null;
  private pendingRequests = new Map<string, WorkerPromise>();
  
  constructor() {
    this.initializeWorker();
  }
  
  async parseMarkdown(content: string, options: ParseOptions): Promise<string> {
    if (!this.worker) throw new Error('Worker not available');
    
    const id = crypto.randomUUID();
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      
      this.worker!.postMessage({
        type: 'parse',
        id,
        payload: { content, options }
      });
      
      // Timeout for very large files (30 seconds)
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('Worker timeout'));
        }
      }, 30000);
    });
  }
  
  private initializeWorker() {
    this.worker = new Worker(
      new URL('../workers/markdown-worker.ts', import.meta.url),
      { type: 'module' }
    );
    
    this.worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
      const { type, id, payload } = event.data;
      const request = this.pendingRequests.get(id);
      
      if (!request) return;
      
      switch (type) {
        case 'parsed':
          this.pendingRequests.delete(id);
          request.resolve(payload.html!);
          break;
        case 'error':
          this.pendingRequests.delete(id);
          request.reject(new Error(payload.error));
          break;
        case 'progress':
          // Could emit progress events here
          break;
      }
    };
  }
}
```

**Performance Characteristics:**
- Files < 50KB: Main thread processing (~5-15ms)
- Files 50-200KB: Web Worker processing (~20-100ms) 
- UI remains responsive during all operations
- Automatic fallback to main thread if Worker fails

## Scroll Synchronization Technical Risk Mitigation

**Problem:** Maintaining accurate scroll position synchronization between CodeMirror editor and HTML preview panes presents significant technical challenges due to:
- Variable line heights in rendered HTML
- Complex markdown structures (tables, code blocks, lists)
- Dynamic content rendering timing
- Cross-browser viewport differences

**Solution Architecture:**

### Line-to-Element Mapping System

```typescript
// Scroll Synchronization Manager
class ScrollSyncManager {
  private editorToPreviewMap = new Map<number, number>();
  private previewToEditorMap = new Map<number, number>();
  private isUserScrolling = false;
  private scrollTimeout: NodeJS.Timeout | null = null;
  
  constructor(
    private editor: EditorView,
    private previewElement: HTMLElement
  ) {
    this.setupScrollListeners();
  }
  
  // Create mapping between editor lines and preview elements
  createScrollMap(editorContent: string, previewHTML: string) {
    const lines = editorContent.split('\n');
    const previewElements = this.getPreviewElements();
    
    let currentLine = 0;
    let currentElement = 0;
    
    // Parse markdown structure to create line mappings
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Headers create reliable anchor points
      if (line.match(/^#{1,6}\s/)) {
        const headerLevel = line.match(/^(#{1,6})/)?.[1].length || 1;
        const element = this.findHeaderElement(previewElements, headerLevel, i);
        if (element) {
          this.mapLineToElement(i, element);
          currentElement = element;
        }
      }
      
      // Code blocks are reliable markers
      else if (line.match(/^```/)) {
        const codeElement = this.findCodeBlock(previewElements, currentElement);
        if (codeElement) {
          this.mapLineToElement(i, codeElement);
        }
      }
      
      // Tables have distinct structures
      else if (line.includes('|')) {
        const tableElement = this.findTableElement(previewElements, currentElement);
        if (tableElement) {
          this.mapLineToElement(i, tableElement);
        }
      }
      
      // Fill gaps with interpolation
      else {
        this.interpolateMapping(i);
      }
    }
  }
  
  // Bidirectional scroll synchronization
  syncEditorToPreview(editorLine: number) {
    if (this.isUserScrolling) return;
    
    const targetElement = this.findNearestPreviewElement(editorLine);
    if (!targetElement) return;
    
    const elementTop = targetElement.offsetTop;
    const containerHeight = this.previewElement.clientHeight;
    const scrollTop = Math.max(0, elementTop - containerHeight / 3);
    
    this.isUserScrolling = true;
    this.previewElement.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
    
    this.resetScrollingFlag();
  }
  
  syncPreviewToEditor(previewScrollTop: number) {
    if (this.isUserScrolling) return;
    
    const targetLine = this.findNearestEditorLine(previewScrollTop);
    if (targetLine === null) return;
    
    this.isUserScrolling = true;
    this.editor.dispatch({
      effects: EditorView.scrollIntoView(
        this.editor.state.doc.line(targetLine + 1).from,
        { y: 'center' }
      )
    });
    
    this.resetScrollingFlag();
  }
  
  // Fallback strategies for difficult cases
  private findNearestPreviewElement(line: number): HTMLElement | null {
    // Try exact mapping first
    if (this.editorToPreviewMap.has(line)) {
      return this.getElementByIndex(this.editorToPreviewMap.get(line)!);
    }
    
    // Find nearest mapped line
    let nearestLine = line;
    let searchRadius = 1;
    
    while (searchRadius < 50) { // Reasonable search limit
      const above = line - searchRadius;
      const below = line + searchRadius;
      
      if (this.editorToPreviewMap.has(above)) {
        nearestLine = above;
        break;
      }
      
      if (this.editorToPreviewMap.has(below)) {
        nearestLine = below;
        break;
      }
      
      searchRadius++;
    }
    
    return this.getElementByIndex(this.editorToPreviewMap.get(nearestLine)!);
  }
  
  // Handle user-initiated scrolling conflicts
  private setupScrollListeners() {
    // Editor scroll handler
    this.editor.scrollDOM.addEventListener('scroll', () => {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);
      
      this.scrollTimeout = setTimeout(() => {
        const { from } = this.editor.viewport;
        const line = this.editor.state.doc.lineAt(from).number - 1;
        this.syncEditorToPreview(line);
      }, 100);
    });
    
    // Preview scroll handler
    this.previewElement.addEventListener('scroll', () => {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);
      
      this.scrollTimeout = setTimeout(() => {
        this.syncPreviewToEditor(this.previewElement.scrollTop);
      }, 100);
    });
  }
  
  private resetScrollingFlag() {
    setTimeout(() => {
      this.isUserScrolling = false;
    }, 300);
  }
}
```

**Key Architectural Decisions:**
- **Anchor Point Strategy:** Headers, code blocks, and tables serve as reliable synchronization anchors
- **Interpolation Fallback:** Gaps between anchors filled with proportional positioning
- **User Intent Preservation:** Scroll conflict resolution prioritizes user-initiated scrolling
- **Performance Optimization:** 100ms debouncing prevents excessive synchronization calls
- **Graceful Degradation:** System remains functional even with imperfect mappings

### Epic 4: PWA Architecture

**Progressive Web App Implementation:**

```typescript
// PWA Service Worker Strategy
interface PWAConfig {
  cacheStrategy: 'networkFirst' | 'cacheFirst';
  offlinePages: string[];
  staticAssets: string[];
  dynamicCaching: boolean;
}

// Service Worker Registration
class PWAManager {
  private registration: ServiceWorkerRegistration | null = null;
  
  async initialize() {
    if ('serviceWorker' in navigator) {
      try {
        this.registration = await navigator.serviceWorker.register(
          '/sw.js',
          { scope: '/' }
        );
        
        this.setupUpdateHandling();
        this.setupOfflineHandling();
      } catch (error) {
        console.warn('PWA not available:', error);
      }
    }
  }
  
  private setupUpdateHandling() {
    this.registration?.addEventListener('updatefound', () => {
      const newWorker = this.registration!.installing;
      
      newWorker?.addEventListener('statechange', () => {
        if (newWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            // Show update notification
            this.showUpdateAvailable();
          }
        }
      });
    });
  }
  
  // Offline file storage using IndexedDB
  async storeFileOffline(filename: string, content: string) {
    const db = await this.openOfflineDB();
    const transaction = db.transaction(['files'], 'readwrite');
    const store = transaction.objectStore('files');
    
    await store.put({
      id: filename,
      content,
      timestamp: Date.now(),
      synced: false
    });
  }
  
  async getOfflineFiles(): Promise<OfflineFile[]> {
    const db = await this.openOfflineDB();
    const transaction = db.transaction(['files'], 'readonly');
    const store = transaction.objectStore('files');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}
```

**Offline Capabilities:**
- Complete application caching for offline usage
- IndexedDB storage for user content persistence
- Background sync for future cloud storage integration
- Offline-first editing with sync indicators

## Cross-Browser Drag-and-Drop Consistency

**Problem:** Drag-and-drop behavior varies significantly across browsers, particularly:
- Safari's restrictive file access policies
- Firefox's different drag event handling
- Mobile browser limitations
- Inconsistent drag feedback states

**Solution Architecture:**

```typescript
// Cross-Browser Drag-and-Drop Handler
class UniversalDropHandler {
  private dragCounter = 0;
  private supportedTypes = ['text/markdown', 'text/plain'];
  
  constructor(private dropZone: HTMLElement) {
    this.setupEventListeners();
  }
  
  private setupEventListeners() {
    // Prevent default browser behavior
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.preventDefaults);
      document.body.addEventListener(eventName, this.preventDefaults);
    });
    
    // Handle drag enter/leave with counter for nested elements
    this.dropZone.addEventListener('dragenter', this.handleDragEnter.bind(this));
    this.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
    this.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
    this.dropZone.addEventListener('drop', this.handleDrop.bind(this));
  }
  
  private preventDefaults(e: Event) {
    e.preventDefault();
    e.stopPropagation();
  }
  
  private handleDragEnter(e: DragEvent) {
    this.dragCounter++;
    
    if (this.validateDraggedItems(e.dataTransfer)) {
      this.dropZone.classList.add('drag-valid');
      this.showDropFeedback(true);
    } else {
      this.dropZone.classList.add('drag-invalid');
      this.showDropFeedback(false);
    }
  }
  
  private handleDragLeave() {
    this.dragCounter--;
    
    if (this.dragCounter === 0) {
      this.dropZone.classList.remove('drag-valid', 'drag-invalid');
      this.hideDropFeedback();
    }
  }
  
  private async handleDrop(e: DragEvent) {
    this.dragCounter = 0;
    this.dropZone.classList.remove('drag-valid', 'drag-invalid');
    this.hideDropFeedback();
    
    const files = Array.from(e.dataTransfer?.files || []);
    
    if (files.length === 0) {
      this.handleTextDrop(e.dataTransfer?.getData('text/plain') || '');
      return;
    }
    
    // Process first file only (MVP limitation)
    const file = files[0];
    
    try {
      const content = await this.readFile(file);
      this.onFileLoaded(content, file.name);
    } catch (error) {
      this.onError(new Error(`Failed to read file: ${error.message}`));
    }
  }
  
  // Cross-browser file reading
  private readFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      // Validate file type and size
      if (!this.isValidFile(file)) {
        reject(new Error(`Unsupported file type: ${file.type}`));
        return;
      }
      
      if (file.size > 209715200) { // 200MB limit
        reject(new Error('File too large (max 200MB)'));
        return;
      }
      
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const result = event.target?.result;
        if (typeof result === 'string') {
          resolve(result);
        } else {
          reject(new Error('Failed to read file as text'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('File reading failed'));
      };
      
      // Handle different encoding detection
      reader.readAsText(file, 'UTF-8');
    });
  }
  
  // Browser-specific validation
  private validateDraggedItems(dataTransfer: DataTransfer | null): boolean {
    if (!dataTransfer) return false;
    
    // Check for files
    if (dataTransfer.files.length > 0) {
      return Array.from(dataTransfer.files).some(file => this.isValidFile(file));
    }
    
    // Check for dragged text content
    const types = dataTransfer.types;
    return types.includes('text/plain') || types.includes('text/markdown');
  }
  
  private isValidFile(file: File): boolean {
    const validTypes = [
      'text/markdown',
      'text/plain',
      'application/octet-stream' // Fallback for .md files
    ];
    
    const validExtensions = ['.md', '.txt', '.markdown'];
    
    return validTypes.includes(file.type) || 
           validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  }
}
```

**Cross-Browser Compatibility Features:**
- **Unified Event Handling:** Consistent drag/drop behavior across all browsers
- **Fallback Mechanisms:** Text drag support when file APIs are limited
- **Mobile Adaptation:** Touch-friendly file selection fallback for mobile devices
- **Progressive Enhancement:** Works without JavaScript, enhanced with JavaScript

## Component Architecture

### Core Component Hierarchy

```mermaid
graph TD
    A[App] --> B[Layout]
    B --> C[Header]
    B --> D[MainEditor]
    B --> E[StatusBar]
    
    D --> F{Epic Check}
    F -->|Epic 1| G[BasicEditor]
    F -->|Epic 2+| H[AdvancedEditor]
    
    D --> I[PreviewPane]
    D --> J[SplitPaneController]
    
    H --> K[CodeMirrorWrapper]
    H --> L[SyntaxHighlighter]
    H --> M[SearchOverlay]
    
    I --> N[MarkdownRenderer]
    I --> O[ScrollSyncHandler]
    
    A --> P[FileDropZone]
    A --> Q[ErrorBoundary]
    A --> R[PWAManager]
    A --> S[WorkerManager]
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style H fill:#fff3e0
    style I fill:#e8f5e8
```

### Component Specifications

**App Component (Root)**
```typescript
interface AppProps {
  initialTheme?: 'light' | 'dark' | 'system';
}

interface AppState {
  currentFile: FileData | null;
  theme: ThemeMode;
  epic: 1 | 2 | 3 | 4;
  isOffline: boolean;
}

// Progressive Epic Detection
const detectAvailableEpic = (): number => {
  if (typeof Worker !== 'undefined' && 'serviceWorker' in navigator) return 4;
  if (window.CodeMirror || import.meta.env.VITE_EPIC_LEVEL >= 3) return 3;
  if (import.meta.env.VITE_EPIC_LEVEL >= 2) return 2;
  return 1;
};
```

**MainEditor Component (Epic Progression)**
```typescript
// Epic 1: Basic Editor
const BasicEditor = ({ value, onChange }: EditorProps) => (
  <textarea
    value={value}
    onChange={(e) => onChange(e.target.value)}
    className="font-mono resize-none w-full h-full p-4"
    placeholder="Start typing your markdown..."
  />
);

// Epic 2+: Advanced Editor
const AdvancedEditor = ({ value, onChange, onScroll }: AdvancedEditorProps) => {
  const extensions = useMemo(() => [
    markdown(),
    syntaxHighlighting(defaultHighlightStyle),
    lineNumbers(),
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        onChange(update.state.doc.toString());
      }
      if (update.scrolled) {
        onScroll?.(update.view);
      }
    })
  ], [onChange, onScroll]);

  return (
    <CodeMirror
      value={value}
      extensions={extensions}
      theme={theme === 'dark' ? oneDark : undefined}
    />
  );
};
```

**Preview Pane Component**
```typescript
interface PreviewPaneProps {
  content: string;
  isLoading: boolean;
  onScroll: (scrollTop: number) => void;
}

const PreviewPane = ({ content, isLoading, onScroll }: PreviewPaneProps) => {
  const [html, setHtml] = useState('');
  const syncManager = useScrollSync();
  const workerManager = useWorkerManager();
  
  useEffect(() => {
    const processContent = async () => {
      if (content.length >= 51200) {
        // Use Web Worker for large files
        const result = await workerManager.parseMarkdown(content);
        setHtml(result);
      } else {
        // Process on main thread
        const result = marked(content, { gfm: true });
        setHtml(result);
      }
    };
    
    const debounceTimer = setTimeout(processContent, 100);
    return () => clearTimeout(debounceTimer);
  }, [content, workerManager]);
  
  return (
    <div 
      className="preview-pane overflow-auto p-4"
      onScroll={(e) => onScroll(e.currentTarget.scrollTop)}
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        </div>
      ) : (
        <div 
          dangerouslySetInnerHTML={{ __html: html }}
          className="prose prose-lg max-w-none"
        />
      )}
    </div>
  );
};
```

## Project Structure

```
mdedit/
├── .github/
│   └── workflows/
│       ├── ci.yml                  # Test and build validation
│       └── deploy.yml              # Deploy to Vercel
├── public/
│   ├── manifest.json               # PWA manifest
│   ├── sw.js                       # Service worker
│   └── icons/                      # PWA icons
├── src/
│   ├── components/
│   │   ├── ui/                     # Base UI components
│   │   │   ├── Button.tsx
│   │   │   ├── Modal.tsx
│   │   │   └── Spinner.tsx
│   │   ├── editor/                 # Editor components
│   │   │   ├── BasicEditor.tsx     # Epic 1 editor
│   │   │   ├── AdvancedEditor.tsx  # Epic 2+ editor
│   │   │   ├── CodeMirrorWrapper.tsx
│   │   │   └── SearchOverlay.tsx
│   │   ├── preview/                # Preview components
│   │   │   ├── PreviewPane.tsx
│   │   │   ├── MarkdownRenderer.tsx
│   │   │   └── ScrollSyncHandler.tsx
│   │   ├── layout/                 # Layout components
│   │   │   ├── Header.tsx
│   │   │   ├── MainEditor.tsx
│   │   │   ├── SplitPane.tsx
│   │   │   └── StatusBar.tsx
│   │   └── file/                   # File handling
│   │       ├── FileDropZone.tsx
│   │       ├── DropHandler.tsx
│   │       └── FileValidator.tsx
│   ├── hooks/                      # Custom React hooks
│   │   ├── useFileHandler.ts
│   │   ├── useScrollSync.ts
│   │   ├── useWorkerManager.ts
│   │   ├── usePWA.ts
│   │   └── useEpicDetection.ts
│   ├── services/                   # Business logic services
│   │   ├── MarkdownParser.ts
│   │   ├── WorkerManager.ts
│   │   ├── ScrollSyncManager.ts
│   │   ├── PWAManager.ts
│   │   └── StorageManager.ts
│   ├── workers/                    # Web Workers
│   │   └── markdown-worker.ts
│   ├── types/                      # TypeScript definitions
│   │   ├── editor.ts
│   │   ├── file.ts
│   │   └── worker.ts
│   ├── utils/                      # Utility functions
│   │   ├── file-utils.ts
│   │   ├── markdown-utils.ts
│   │   └── performance.ts
│   ├── styles/                     # Global styles
│   │   ├── globals.css
│   │   ├── components.css
│   │   └── themes.css
│   ├── App.tsx                     # Root component
│   ├── main.tsx                    # Entry point
│   └── vite-env.d.ts               # Vite types
├── tests/
│   ├── unit/                       # Unit tests
│   ├── integration/                # Integration tests
│   └── e2e/                        # Playwright E2E tests
├── docs/
│   ├── prd.md                      # Product requirements
│   ├── front-end-spec.md          # UI/UX specification
│   └── architecture.md            # This document
├── .env.example                    # Environment template
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.js
├── eslint.config.js
├── playwright.config.ts
└── README.md
```

## Performance and Deployment Strategy

### Bundle Optimization Strategy

**Epic 1 Bundle (~45KB gzipped):**
- Core React + DOM handling
- Basic markdown parsing
- Essential UI components
- Basic drag-and-drop

**Epic 2 Enhancements (Lazy loaded ~85KB):**
- CodeMirror 6 editor
- Advanced syntax highlighting
- Real-time sync managers
- Scroll synchronization

**Epic 3+ Features (On-demand):**
- Web Worker implementations
- Advanced export capabilities
- Theme system enhancements
- PWA service worker

**Performance Monitoring:**
```typescript
// Performance tracking for key metrics
class PerformanceTracker {
  static trackFileProcessing(fileSize: number, processingTime: number) {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'file_processing_time', {
        custom_map: { metric1: 'file_size', metric2: 'processing_time' },
        metric1: fileSize,
        metric2: processingTime
      });
    }
  }
  
  static trackPreviewLatency(updateTime: number) {
    if (updateTime > 100) { // Above target threshold
      console.warn(`Preview update exceeded 100ms: ${updateTime}ms`);
    }
  }
}
```

### Deployment Configuration

**Vite Production Build:**
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          }
        ]
      }
    })
  ],
  build: {
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          codemirror: ['@codemirror/state', '@codemirror/view', '@codemirror/lang-markdown'],
          markdown: ['marked', 'marked-gfm']
        }
      }
    }
  }
});
```

**Vercel Configuration:**
```json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm install",
  "devCommand": "npm run dev",
  "headers": [
    {
      "source": "/service-worker.js",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=0, must-revalidate"
        }
      ]
    }
  ]
}
```

## Technical Risk Assessment Summary

### Identified Risks and Mitigation Strategies

**HIGH RISK - ADDRESSED:**
1. **Scroll Synchronization Complexity** ✅ 
   - **Solution:** Line-to-element mapping with anchor point strategy
   - **Fallback:** Proportional positioning for unmapped content
   - **Testing:** Comprehensive test cases for complex markdown structures

2. **CodeMirror Bundle Size Impact** ✅
   - **Solution:** Lazy loading and code splitting by epic
   - **Monitoring:** Bundle size tracking in CI pipeline
   - **Performance:** Progressive enhancement maintains fast initial load

3. **Web Worker Browser Compatibility** ✅
   - **Solution:** Feature detection with main thread fallback
   - **Testing:** Cross-browser compatibility testing
   - **Graceful Degradation:** Full functionality without workers

**MEDIUM RISK - MONITORED:**
1. **Large File Memory Management**
   - **Mitigation:** File size warnings, chunked processing, garbage collection
   - **Monitoring:** Memory usage tracking and alerts

2. **Cross-Browser Drag-and-Drop Consistency**  
   - **Mitigation:** Universal event handler with browser-specific adaptations
   - **Testing:** Automated tests across all supported browsers

**LOW RISK:**
1. **PWA Installation and Offline Storage**
   - **Fallback:** Application works fully without PWA features
   - **Progressive Enhancement:** PWA features enhance but don't break core functionality

This comprehensive technical architecture addresses all requirements from the PRD and front-end specification, providing a solid foundation for the four-epic development progression while mitigating the identified technical risks through proven architectural patterns and robust implementation strategies.
