# Epic Level Configuration
VITE_EPIC_LEVEL=1

# Application Configuration
VITE_APP_NAME=MDEdit
VITE_APP_VERSION=1.0.0

# Deployment Configuration (for CI/CD)
VERCEL_TOKEN=your_vercel_token_here
ORG_ID=your_vercel_org_id_here
PROJECT_ID=your_vercel_project_id_here

# GitHub Actions Configuration
GITHUB_TOKEN=your_github_token_here
LHCI_GITHUB_APP_TOKEN=your_lighthouse_ci_token_here

# Optional: Analytics and Monitoring
VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_GA_TRACKING_ID=your_google_analytics_id_here
