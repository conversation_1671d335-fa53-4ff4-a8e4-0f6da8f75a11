import { MarkdownParser } from '../../../src/services/MarkdownParser';

// Mock the marked library
vi.mock('marked', () => {
  const mockRenderer = {
    code: vi.fn(),
    codespan: vi.fn(),
    image: vi.fn(),
    link: vi.fn(),
    blockquote: vi.fn(),
    table: vi.fn(),
  };

  return {
    marked: vi.fn(),
    Renderer: vi.fn().mockImplementation(() => mockRenderer),
    setOptions: vi.fn(),
    use: vi.fn(),
  };
});

const { marked } = await import('marked');
const mockMarked = marked as any;

describe('MarkdownParser', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockMarked.mockReturnValue('<h1>Hello World</h1><p>This is a paragraph.</p>');
  });

  describe('parse', () => {
    it('parses basic markdown correctly', () => {
      const result = MarkdownParser.parse('# Hello World\n\nThis is a paragraph.');

      expect(result.html).toContain('<h1');
      expect(result.html).toContain('Hello World');
      expect(result.html).toContain('<p>');
      expect(result.html).toContain('This is a paragraph.');
      expect(result.processingTime).toBeGreaterThan(0);
      expect(result.error).toBeUndefined();
    });

    it('handles empty content', () => {
      const result = MarkdownParser.parse('');
      
      expect(result.html).toContain('empty-preview');
      expect(result.html).toContain('Start editing to see preview');
      expect(result.error).toBeUndefined();
    });

    it('handles whitespace-only content', () => {
      const result = MarkdownParser.parse('   \n\t  \n  ');
      
      expect(result.html).toContain('empty-preview');
      expect(result.error).toBeUndefined();
    });

    it('parses code blocks with language', () => {
      const markdown = '```javascript\nconst x = 1;\n```';
      const result = MarkdownParser.parse(markdown);
      
      expect(result.html).toContain('code-block');
      expect(result.html).toContain('language-javascript');
      expect(result.html).toContain('const x = 1;');
    });

    it('parses inline code', () => {
      const result = MarkdownParser.parse('This is `inline code` here.');
      
      expect(result.html).toContain('inline-code');
      expect(result.html).toContain('inline code');
    });

    it('parses images with fallback', () => {
      const result = MarkdownParser.parse('![Alt text](https://example.com/image.jpg "Title")');
      
      expect(result.html).toContain('markdown-image');
      expect(result.html).toContain('alt="Alt text"');
      expect(result.html).toContain('title="Title"');
      expect(result.html).toContain('image-fallback');
      expect(result.html).toContain('onerror');
    });

    it('parses links with external target', () => {
      const result = MarkdownParser.parse('[External](https://example.com) and [Internal](/path)');
      
      expect(result.html).toContain('target="_blank"');
      expect(result.html).toContain('rel="noopener noreferrer"');
      expect(result.html).toContain('markdown-link');
    });

    it('parses blockquotes', () => {
      const result = MarkdownParser.parse('> This is a quote');
      
      expect(result.html).toContain('markdown-blockquote');
      expect(result.html).toContain('This is a quote');
    });

    it('parses tables', () => {
      const markdown = `| Header 1 | Header 2 |
|----------|----------|
| Cell 1   | Cell 2   |`;
      
      const result = MarkdownParser.parse(markdown);
      
      expect(result.html).toContain('table-wrapper');
      expect(result.html).toContain('markdown-table');
      expect(result.html).toContain('Header 1');
      expect(result.html).toContain('Cell 1');
    });

    it('handles parsing errors gracefully', () => {
      // Mock marked to throw an error
      mockMarked.mockImplementation(() => {
        throw new Error('Parse error');
      });

      const result = MarkdownParser.parse('# Test');

      expect(result.html).toContain('error-preview');
      expect(result.html).toContain('Markdown Parsing Error');
      expect(result.error).toBe('Parse error');
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('escapes HTML in code blocks', () => {
      const markdown = '```html\n<script>alert("xss")</script>\n```';
      const result = MarkdownParser.parse(markdown);
      
      expect(result.html).toContain('&lt;script&gt;');
      expect(result.html).toContain('&quot;xss&quot;');
      expect(result.html).not.toContain('<script>alert("xss")</script>');
    });

    it('escapes HTML in inline code', () => {
      mockMarked.mockReturnValue('<p>Use <code class="inline-code">&lt;div&gt;</code> tags</p>');

      const result = MarkdownParser.parse('Use `<div>` tags');

      expect(result.html).toContain('&lt;div&gt;');
      expect(result.html).not.toContain('<div>');
    });
  });

  describe('parseAsync', () => {
    it('returns a promise that resolves with parse result', async () => {
      const result = await MarkdownParser.parseAsync('# Async Test');
      
      expect(result.html).toContain('<h1');
      expect(result.html).toContain('Async Test');
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('handles async parsing errors', async () => {
      // Mock marked to throw an error
      mockMarked.mockImplementation(() => {
        throw new Error('Async parse error');
      });

      const result = await MarkdownParser.parseAsync('# Test');

      expect(result.error).toBe('Async parse error');
      expect(result.html).toContain('error-preview');
    });
  });

  describe('isLargeContent', () => {
    it('returns false for small content', () => {
      expect(MarkdownParser.isLargeContent('Small content')).toBe(false);
    });

    it('returns true for large content', () => {
      const largeContent = 'x'.repeat(60000);
      expect(MarkdownParser.isLargeContent(largeContent)).toBe(true);
    });

    it('returns false for content at threshold', () => {
      const thresholdContent = 'x'.repeat(50000);
      expect(MarkdownParser.isLargeContent(thresholdContent)).toBe(false);
    });

    it('returns true for content above threshold', () => {
      const aboveThresholdContent = 'x'.repeat(50001);
      expect(MarkdownParser.isLargeContent(aboveThresholdContent)).toBe(true);
    });
  });

  describe('estimateProcessingTime', () => {
    it('returns minimum time for very small content', () => {
      expect(MarkdownParser.estimateProcessingTime('')).toBe(10);
      expect(MarkdownParser.estimateProcessingTime('small')).toBe(10);
    });

    it('estimates time based on content length', () => {
      const content1000 = 'x'.repeat(1000);
      const content5000 = 'x'.repeat(5000);
      
      expect(MarkdownParser.estimateProcessingTime(content1000)).toBe(10); // min 10ms
      expect(MarkdownParser.estimateProcessingTime(content5000)).toBe(10); // min 10ms
      
      const content20000 = 'x'.repeat(20000);
      expect(MarkdownParser.estimateProcessingTime(content20000)).toBe(20);
    });

    it('scales with content size', () => {
      const content100k = 'x'.repeat(100000);
      expect(MarkdownParser.estimateProcessingTime(content100k)).toBe(100);
    });
  });
});
