import { renderHook, act } from '@testing-library/react';
import { useResponsiveLayout } from '../../../src/hooks/useResponsiveLayout';

// Mock window.innerWidth and window.innerHeight
const mockWindowSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
};

describe('useResponsiveLayout', () => {
  beforeEach(() => {
    // Reset to desktop size
    mockWindowSize(1200, 800);
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should detect desktop layout for large screens', () => {
    mockWindowSize(1200, 800);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.mode).toBe('desktop');
    expect(result.current.isDesktop).toBe(true);
    expect(result.current.isTablet).toBe(false);
    expect(result.current.isMobile).toBe(false);
  });

  it('should detect tablet layout for medium screens', () => {
    mockWindowSize(800, 600);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.mode).toBe('tablet');
    expect(result.current.isTablet).toBe(true);
    expect(result.current.isDesktop).toBe(false);
    expect(result.current.isMobile).toBe(false);
  });

  it('should detect mobile layout for small screens', () => {
    mockWindowSize(400, 600);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.mode).toBe('mobile');
    expect(result.current.isMobile).toBe(true);
    expect(result.current.isTablet).toBe(false);
    expect(result.current.isDesktop).toBe(false);
  });

  it('should update layout when window is resized', () => {
    mockWindowSize(1200, 800);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.mode).toBe('desktop');
    
    // Simulate resize to mobile
    act(() => {
      mockWindowSize(400, 600);
      window.dispatchEvent(new Event('resize'));
    });
    
    expect(result.current.mode).toBe('mobile');
    expect(result.current.width).toBe(400);
    expect(result.current.height).toBe(600);
  });

  it('should handle orientation change events', () => {
    mockWindowSize(800, 600);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.mode).toBe('tablet');
    
    // Simulate orientation change
    act(() => {
      mockWindowSize(600, 800);
      window.dispatchEvent(new Event('orientationchange'));
      
      // Simulate the timeout in the hook
      vi.advanceTimersByTime(100);
    });
    
    expect(result.current.width).toBe(600);
    expect(result.current.height).toBe(800);
  });

  it('should provide correct width and height values', () => {
    mockWindowSize(1024, 768);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.width).toBe(1024);
    expect(result.current.height).toBe(768);
  });

  it('should handle edge cases at breakpoint boundaries', () => {
    // Test exactly at mobile/tablet boundary (768px)
    mockWindowSize(768, 600);
    const { result } = renderHook(() => useResponsiveLayout());
    
    expect(result.current.mode).toBe('tablet');
    
    // Test exactly at tablet/desktop boundary (1024px)
    act(() => {
      mockWindowSize(1024, 600);
      window.dispatchEvent(new Event('resize'));
    });
    
    expect(result.current.mode).toBe('desktop');
  });
});
