import { renderHook, act } from '@testing-library/react';
import { useUndoRedo } from '../../../src/hooks/useUndoRedo';

describe('useUndoRedo', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should initialize with the provided initial value', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    expect(result.current[0]).toBe('initial');
    expect(result.current[1].canUndo).toBe(false);
    expect(result.current[1].canRedo).toBe(false);
  });

  it('should update the current value when set is called', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    act(() => {
      result.current[1].set('new value');
    });
    
    expect(result.current[0]).toBe('new value');
  });

  it('should save to history after time threshold', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    act(() => {
      result.current[1].set('first change');
    });
    
    // Advance time past threshold
    act(() => {
      vi.advanceTimersByTime(1100);
    });
    
    act(() => {
      result.current[1].set('second change');
    });
    
    expect(result.current[0]).toBe('second change');
    expect(result.current[1].canUndo).toBe(true);
  });

  it('should perform undo operation correctly', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    act(() => {
      result.current[1].set('first change');
    });
    
    act(() => {
      vi.advanceTimersByTime(1100);
    });
    
    act(() => {
      result.current[1].set('second change');
    });
    
    act(() => {
      result.current[1].undo();
    });
    
    expect(result.current[0]).toBe('first change');
    expect(result.current[1].canRedo).toBe(true);
  });

  it('should perform redo operation correctly', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    act(() => {
      result.current[1].set('first change');
    });
    
    act(() => {
      vi.advanceTimersByTime(1100);
    });
    
    act(() => {
      result.current[1].set('second change');
    });
    
    act(() => {
      result.current[1].undo();
    });
    
    act(() => {
      result.current[1].redo();
    });
    
    expect(result.current[0]).toBe('second change');
    expect(result.current[1].canRedo).toBe(false);
  });

  it('should clear future when new content is added after undo', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    act(() => {
      result.current[1].set('first change');
    });
    
    act(() => {
      vi.advanceTimersByTime(1100);
    });
    
    act(() => {
      result.current[1].set('second change');
    });
    
    act(() => {
      result.current[1].undo();
    });
    
    expect(result.current[1].canRedo).toBe(true);
    
    act(() => {
      vi.advanceTimersByTime(1100);
    });
    
    act(() => {
      result.current[1].set('new branch');
    });
    
    expect(result.current[1].canRedo).toBe(false);
  });

  it('should limit history size', () => {
    const { result } = renderHook(() => useUndoRedo('initial'));
    
    // Add more than MAX_HISTORY_SIZE entries
    for (let i = 0; i < 55; i++) {
      act(() => {
        result.current[1].set(`change ${i}`);
      });
      
      act(() => {
        vi.advanceTimersByTime(1100);
      });
    }
    
    // Should still be able to undo, but not more than MAX_HISTORY_SIZE times
    let undoCount = 0;
    while (result.current[1].canUndo && undoCount < 60) {
      act(() => {
        result.current[1].undo();
      });
      undoCount++;
    }
    
    expect(undoCount).toBeLessThanOrEqual(50); // MAX_HISTORY_SIZE
  });
});
