import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from '../../src/App';

describe('App', () => {
  it('renders the application header', () => {
    render(<App />);
    
    expect(screen.getByText('MDEdit')).toBeInTheDocument();
    expect(screen.getByText('Epic 1')).toBeInTheDocument();
  });

  it('renders the empty state when no file is loaded', () => {
    render(<App />);
    
    expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
    expect(screen.getByText('Choose File')).toBeInTheDocument();
    expect(screen.getByText('New Document')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<App />);
    
    const themeToggle = screen.getByLabelText('Toggle theme');
    expect(themeToggle).toBeInTheDocument();
  });
});
