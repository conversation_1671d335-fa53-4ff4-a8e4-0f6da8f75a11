import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PreviewPane } from '../../../src/components/preview/PreviewPane';

// Mock the MarkdownParser
vi.mock('../../../src/services/MarkdownParser', () => ({
  MarkdownParser: {
    parseAsync: vi.fn(),
    isLargeContent: vi.fn(),
    estimateProcessingTime: vi.fn(),
  }
}));

import { MarkdownParser } from '../../../src/services/MarkdownParser';

const mockMarkdownParser = MarkdownParser as any;

describe('PreviewPane', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockMarkdownParser.parseAsync.mockResolvedValue({
      html: '<p>Test content</p>',
      processingTime: 10
    });
    mockMarkdownParser.isLargeContent.mockReturnValue(false);
    mockMarkdownParser.estimateProcessingTime.mockReturnValue(10);
  });

  it('renders the preview pane with header', () => {
    render(<PreviewPane content="# Hello World" />);
    
    expect(screen.getByText('Preview')).toBeInTheDocument();
  });

  it('shows manual update button when autoUpdate is false', async () => {
    render(<PreviewPane content="# Hello World" autoUpdate={false} />);

    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  it('does not show update button when autoUpdate is true', async () => {
    render(<PreviewPane content="# Hello World" autoUpdate={true} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Refresh')).not.toBeInTheDocument();
    });
  });

  it('updates preview when update button is clicked', async () => {
    mockMarkdownParser.parseAsync.mockResolvedValue({
      html: '<h1>Hello World</h1>',
      processingTime: 15
    });

    render(<PreviewPane content="# Hello World" autoUpdate={false} />);

    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    const updateButton = screen.getByRole('button');
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(mockMarkdownParser.parseAsync).toHaveBeenCalledWith('# Hello World');
    });
  });

  it('shows loading state during update', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });
    
    mockMarkdownParser.parseAsync.mockReturnValue(promise);

    render(<PreviewPane content="# Hello World" autoUpdate={false} />);
    
    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    const updateButton = screen.getByRole('button');
    fireEvent.click(updateButton);

    expect(screen.getByText('Updating...')).toBeInTheDocument();
    expect(screen.getByText('Rendering preview...')).toBeInTheDocument();

    // Resolve the promise
    resolvePromise!({
      html: '<h1>Hello World</h1>',
      processingTime: 15
    });

    await waitFor(() => {
      expect(screen.queryByText('Updating...')).not.toBeInTheDocument();
    });
  });

  it('shows large content indicator for large files', async () => {
    mockMarkdownParser.isLargeContent.mockReturnValue(true);
    mockMarkdownParser.estimateProcessingTime.mockReturnValue(150);

    render(<PreviewPane content="Large content..." autoUpdate={false} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Large Content/)).toBeInTheDocument();
      expect(screen.getByText(/150ms/)).toBeInTheDocument();
    });
  });

  it('shows processing time after update', async () => {
    mockMarkdownParser.parseAsync.mockResolvedValue({
      html: '<p>Test</p>',
      processingTime: 25.7
    });

    render(<PreviewPane content="Test" autoUpdate={false} />);
    
    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    const updateButton = screen.getByRole('button');
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(screen.getByText('Processed in 25.7ms')).toBeInTheDocument();
    });
  });

  it('shows error message when parsing fails', async () => {
    mockMarkdownParser.parseAsync.mockResolvedValue({
      html: '<div class="error-preview">Error occurred</div>',
      error: 'Parse error',
      processingTime: 5
    });

    render(<PreviewPane content="Bad markdown" autoUpdate={false} />);
    
    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    const updateButton = screen.getByRole('button');
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(screen.getByText('• Error: Parse error')).toBeInTheDocument();
    });
  });

  it('shows changes pending when content changes', async () => {
    const { rerender } = render(<PreviewPane content="Original" autoUpdate={false} />);
    
    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    // Simulate content change
    rerender(<PreviewPane content="Changed content" autoUpdate={false} />);

    await waitFor(() => {
      expect(screen.getByText('Changes pending')).toBeInTheDocument();
      expect(screen.getByText('Update Preview')).toBeInTheDocument();
    });
  });

  it('shows last updated time after successful update', async () => {
    render(<PreviewPane content="Test" autoUpdate={false} />);

    await waitFor(() => {
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    const updateButton = screen.getByRole('button');
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(screen.getByText(/Updated/)).toBeInTheDocument();
    });
  });

  it('auto-updates when autoUpdate is true', async () => {
    render(<PreviewPane content="Auto update test" autoUpdate={true} />);
    
    await waitFor(() => {
      expect(mockMarkdownParser.parseAsync).toHaveBeenCalledWith('Auto update test');
    });
  });

  it('renders parsed HTML content', async () => {
    mockMarkdownParser.parseAsync.mockResolvedValue({
      html: '<h1>Rendered Title</h1><p>Rendered paragraph</p>',
      processingTime: 10
    });

    render(<PreviewPane content="# Title\n\nParagraph" autoUpdate={true} />);
    
    await waitFor(() => {
      const container = document.querySelector('.markdown-body');
      expect(container).toBeInTheDocument();
    });
  });
});
