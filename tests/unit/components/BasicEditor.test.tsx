import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BasicEditor } from '../../../src/components/editor/BasicEditor';

describe('BasicEditor', () => {
  const defaultProps = {
    content: '',
    onChange: vi.fn(),
    placeholder: 'Start typing...',
    disabled: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the editor with content', () => {
    render(<BasicEditor {...defaultProps} content="Hello world" />);
    
    expect(screen.getByDisplayValue('Hello world')).toBeInTheDocument();
    expect(screen.getByText('Editor')).toBeInTheDocument();
  });

  it('shows character count', () => {
    render(<BasicEditor {...defaultProps} content="Hello world" />);
    
    expect(screen.getByText('11 chars')).toBeInTheDocument();
  });

  it('shows large file indicator for content over 50KB', () => {
    const largeContent = 'a'.repeat(51000);
    render(<BasicEditor {...defaultProps} content={largeContent} />);
    
    expect(screen.getByText('Large File')).toBeInTheDocument();
  });

  it('calls onChange when content is typed', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();
    
    render(<BasicEditor {...defaultProps} onChange={onChange} />);
    
    const textarea = screen.getByRole('textbox');
    await user.type(textarea, 'Hello');
    
    expect(onChange).toHaveBeenCalled();
  });

  it('handles keyboard shortcuts for undo/redo', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();
    
    render(<BasicEditor {...defaultProps} onChange={onChange} />);
    
    const textarea = screen.getByRole('textbox');
    
    // Type some content
    await user.type(textarea, 'Hello');
    
    // Wait for undo/redo state to update
    await waitFor(() => {
      expect(onChange).toHaveBeenCalled();
    });
    
    // Test Ctrl+Z (undo)
    await user.keyboard('{Control>}z{/Control}');
    
    // The undo functionality should work (though exact behavior depends on timing)
    expect(onChange).toHaveBeenCalled();
  });

  it('handles Tab key for indentation', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();

    render(<BasicEditor {...defaultProps} onChange={onChange} />);

    const textarea = screen.getByRole('textbox');
    textarea.focus();

    // Press Tab key - this will be handled by the keyboard shortcut handler
    fireEvent.keyDown(textarea, { key: 'Tab', preventDefault: vi.fn() });

    // Should call onChange with spaces
    await waitFor(() => {
      expect(onChange).toHaveBeenCalledWith('  ');
    });
  });

  it('handles Ctrl+A for select all', async () => {
    const user = userEvent.setup();
    
    render(<BasicEditor {...defaultProps} content="Hello world" />);
    
    const textarea = screen.getByRole('textbox') as HTMLTextAreaElement;
    textarea.focus();
    
    // Press Ctrl+A
    await user.keyboard('{Control>}a{/Control}');
    
    // Should select all text
    expect(textarea.selectionStart).toBe(0);
    expect(textarea.selectionEnd).toBe(11);
  });

  it('disables keyboard shortcuts when disabled', () => {
    render(<BasicEditor {...defaultProps} disabled={true} />);
    
    const textarea = screen.getByRole('textbox');
    expect(textarea).toBeDisabled();
  });

  it('applies correct styling and attributes', () => {
    render(<BasicEditor {...defaultProps} />);
    
    const textarea = screen.getByRole('textbox');
    
    expect(textarea).toHaveAttribute('spellCheck', 'false');
    expect(textarea).toHaveAttribute('autoComplete', 'off');
    expect(textarea).toHaveAttribute('autoCorrect', 'off');
    expect(textarea).toHaveAttribute('autoCapitalize', 'off');
    expect(textarea).toHaveAttribute('data-gramm', 'false');
  });

  it('maintains focus and cursor position', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();

    render(<BasicEditor {...defaultProps} onChange={onChange} content="Hello world" />);

    const textarea = screen.getByRole('textbox') as HTMLTextAreaElement;

    // Focus the textarea
    textarea.focus();

    // Set cursor position
    textarea.setSelectionRange(5, 5);

    // The cursor position should be maintained
    expect(textarea.selectionStart).toBe(5);
    expect(textarea.selectionEnd).toBe(5);

    // Verify the textarea is focused
    expect(document.activeElement).toBe(textarea);
  });

  it('shows placeholder when content is empty', () => {
    render(<BasicEditor {...defaultProps} placeholder="Type here..." />);
    
    expect(screen.getByPlaceholderText('Type here...')).toBeInTheDocument();
  });

  it('handles large content without performance issues', () => {
    const largeContent = 'Lorem ipsum '.repeat(10000); // ~120KB

    const startTime = performance.now();
    render(<BasicEditor {...defaultProps} content={largeContent} />);
    const endTime = performance.now();

    // Should render within reasonable time (less than 100ms)
    expect(endTime - startTime).toBeLessThan(100);

    // Check that the textarea contains the content (using a partial match due to size)
    const textarea = screen.getByRole('textbox') as HTMLTextAreaElement;
    expect(textarea.value).toBe(largeContent);
  });
});
