import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FileDropZone } from '../../../src/components/file/FileDropZone';
import type { FileData } from '../../../src/types/file';

// Mock the file utilities
vi.mock('../../../src/utils/file-utils', () => ({
  FileValidator: {
    validate: vi.fn(),
    readFileContent: vi.fn(),
  },
  formatFileSize: vi.fn((bytes: number) => `${bytes} bytes`),
  hasFiles: vi.fn(),
  getFilesFromDragEvent: vi.fn(),
}));

describe('FileDropZone', () => {
  const mockOnFileLoad = vi.fn();
  const mockOnError = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the drop zone with default content', () => {
    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
    expect(screen.getByText('Choose File')).toBeInTheDocument();
    expect(screen.getByText('New Document')).toBeInTheDocument();
  });

  it('shows drag over state when files are dragged over', async () => {
    const { hasFiles } = await import('../../../src/utils/file-utils');
    vi.mocked(hasFiles).mockReturnValue(true);

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);

    // Find the actual drop zone container (the one with drag event handlers)
    const dropZoneContainer = screen.getByText('Drop markdown file here').closest('[class*="border-2"]')!;

    fireEvent.dragEnter(dropZoneContainer);

    expect(dropZoneContainer).toHaveClass('border-primary-500');
    expect(dropZoneContainer).toHaveClass('bg-primary-50');
  });

  it('handles successful file drop', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['# Test content'], 'test.md', { type: 'text/markdown' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ isValid: true });
    vi.mocked(FileValidator.readFileContent).mockResolvedValue('# Test content');

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    await waitFor(() => {
      expect(mockOnFileLoad).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'test.md',
          content: '# Test content',
          size: mockFile.size,
          type: 'text/markdown'
        })
      );
    });
  });

  it('shows error message for invalid files', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ 
      isValid: false, 
      error: 'Invalid file type' 
    });

    render(<FileDropZone onFileLoad={mockOnFileLoad} onError={mockOnError} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    await waitFor(() => {
      expect(screen.getByText('Invalid file type')).toBeInTheDocument();
      expect(mockOnError).toHaveBeenCalledWith('Invalid file type');
    });
  });

  it('shows size warning dialog for large files', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['x'.repeat(300 * 1024)], 'large.md', { type: 'text/markdown' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ 
      isValid: true, 
      warnings: ['File size exceeds recommended limit'] 
    });

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    await waitFor(() => {
      expect(screen.getByText('Large File Warning')).toBeInTheDocument();
      expect(screen.getByText('File size exceeds recommended limit')).toBeInTheDocument();
      expect(screen.getByText('Proceed Anyway')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });
  });

  it('proceeds with large file when user confirms', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['x'.repeat(300 * 1024)], 'large.md', { type: 'text/markdown' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ 
      isValid: true, 
      warnings: ['File size exceeds recommended limit'] 
    });
    vi.mocked(FileValidator.readFileContent).mockResolvedValue('Large content');

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    await waitFor(() => {
      expect(screen.getByText('Proceed Anyway')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Proceed Anyway'));
    
    await waitFor(() => {
      expect(mockOnFileLoad).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'large.md',
          content: 'Large content'
        })
      );
    });
  });

  it('cancels large file upload when user cancels', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['x'.repeat(300 * 1024)], 'large.md', { type: 'text/markdown' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ 
      isValid: true, 
      warnings: ['File size exceeds recommended limit'] 
    });

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    await waitFor(() => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Cancel'));
    
    await waitFor(() => {
      expect(screen.queryByText('Large File Warning')).not.toBeInTheDocument();
    });
    
    expect(mockOnFileLoad).not.toHaveBeenCalled();
  });

  it('handles file selection via input', async () => {
    const { FileValidator } = await import('../../../src/utils/file-utils');

    const mockFile = new File(['# Test'], 'test.md', { type: 'text/markdown' });

    vi.mocked(FileValidator.validate).mockReturnValue({ isValid: true });
    vi.mocked(FileValidator.readFileContent).mockResolvedValue('# Test');

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);

    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;

    await userEvent.upload(fileInput, mockFile);

    await waitFor(() => {
      expect(mockOnFileLoad).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'test.md',
          content: '# Test'
        })
      );
    });
  });

  it('creates new document when clicking New Document button', async () => {
    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    fireEvent.click(screen.getByText('New Document'));
    
    expect(mockOnFileLoad).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'untitled.md',
        content: '# Welcome to MDEdit\n\nStart typing your markdown here...',
        type: 'text/markdown'
      })
    );
  });

  it('shows loading state during file processing', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['# Test'], 'test.md', { type: 'text/markdown' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ isValid: true });
    vi.mocked(FileValidator.readFileContent).mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve('# Test'), 100))
    );

    render(<FileDropZone onFileLoad={mockOnFileLoad} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    expect(screen.getByText('Processing file...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText('Processing file...')).not.toBeInTheDocument();
    });
  });

  it('handles read errors gracefully', async () => {
    const { FileValidator, hasFiles, getFilesFromDragEvent } = await import('../../../src/utils/file-utils');
    
    const mockFile = new File(['# Test'], 'test.md', { type: 'text/markdown' });
    
    vi.mocked(hasFiles).mockReturnValue(true);
    vi.mocked(getFilesFromDragEvent).mockReturnValue([mockFile]);
    vi.mocked(FileValidator.validate).mockReturnValue({ isValid: true });
    vi.mocked(FileValidator.readFileContent).mockRejectedValue(new Error('Read failed'));

    render(<FileDropZone onFileLoad={mockOnFileLoad} onError={mockOnError} />);
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone);
    
    await waitFor(() => {
      expect(screen.getByText('Read failed')).toBeInTheDocument();
      expect(mockOnError).toHaveBeenCalledWith('Read failed');
    });
  });

  it('displays custom accepted types and size limit', () => {
    render(
      <FileDropZone 
        onFileLoad={mockOnFileLoad} 
        acceptedTypes={['.txt']}
        maxSize={100 * 1024}
      />
    );
    
    expect(screen.getByText('.txt')).toBeInTheDocument();
    expect(screen.getByText('Recommended: 102400 bytes')).toBeInTheDocument();
  });
});
