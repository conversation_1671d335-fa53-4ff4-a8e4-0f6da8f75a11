import { describe, it, expect, beforeEach } from 'vitest';
import { FileValidator, formatFileSize, hasFiles, getFilesFromDragEvent } from '../../../src/utils/file-utils';

describe('FileValidator', () => {
  describe('validate', () => {
    it('should accept valid markdown files', () => {
      const file = new File(['# Test'], 'test.md', { type: 'text/markdown' });
      const result = FileValidator.validate(file);
      
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should accept valid text files', () => {
      const file = new File(['Test content'], 'test.txt', { type: 'text/plain' });
      const result = FileValidator.validate(file);
      
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject empty files', () => {
      const file = new File([''], 'empty.md', { type: 'text/markdown' });
      const result = FileValidator.validate(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('empty');
    });

    it('should reject invalid file extensions', () => {
      const file = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const result = FileValidator.validate(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid file type');
    });

    it('should warn about large files but allow them', () => {
      const largeContent = 'x'.repeat(300 * 1024); // 300KB
      const file = new File([largeContent], 'large.md', { type: 'text/markdown' });
      const result = FileValidator.validate(file);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toBeDefined();
      expect(result.warnings![0]).toContain('exceeds recommended limit');
    });

    it('should handle files without MIME type', () => {
      const file = new File(['# Test'], 'test.md', { type: '' });
      const result = FileValidator.validate(file);

      expect(result.isValid).toBe(true);
      // Files with empty MIME type but valid extensions should not generate warnings
      // The warning is only for files with unrecognized MIME types
      expect(result.warnings).toBeUndefined();
    });

    it('should respect custom validation options', () => {
      const file = new File(['content'], 'test.md', { type: 'text/markdown' });
      const result = FileValidator.validate(file, {
        maxSize: 1, // 1 byte
        acceptedExtensions: ['.txt']
      });
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid file type');
    });
  });

  describe('detectEncoding', () => {
    it('should detect UTF-8 BOM', async () => {
      const utf8Bom = new Uint8Array([0xEF, 0xBB, 0xBF, 0x48, 0x65, 0x6C, 0x6C, 0x6F]);
      const file = new File([utf8Bom], 'test.txt', { type: 'text/plain' });
      
      const result = await FileValidator.detectEncoding(file);
      
      expect(result.encoding).toBe('UTF-8');
      expect(result.confidence).toBe(1.0);
    });

    it('should detect UTF-16 LE BOM', async () => {
      const utf16LeBom = new Uint8Array([0xFF, 0xFE, 0x48, 0x00, 0x65, 0x00]);
      const file = new File([utf16LeBom], 'test.txt', { type: 'text/plain' });
      
      const result = await FileValidator.detectEncoding(file);
      
      expect(result.encoding).toBe('UTF-16LE');
      expect(result.confidence).toBe(1.0);
    });

    it('should detect UTF-16 BE BOM', async () => {
      const utf16BeBom = new Uint8Array([0xFE, 0xFF, 0x00, 0x48, 0x00, 0x65]);
      const file = new File([utf16BeBom], 'test.txt', { type: 'text/plain' });
      
      const result = await FileValidator.detectEncoding(file);
      
      expect(result.encoding).toBe('UTF-16BE');
      expect(result.confidence).toBe(1.0);
    });

    it('should default to UTF-8 for plain text', async () => {
      const plainText = new TextEncoder().encode('Hello World');
      const file = new File([plainText], 'test.txt', { type: 'text/plain' });
      
      const result = await FileValidator.detectEncoding(file);
      
      expect(result.encoding).toBe('UTF-8');
      expect(result.confidence).toBeGreaterThan(0.5);
    });
  });

  describe('readFileContent', () => {
    it('should read UTF-8 content correctly', async () => {
      const content = 'Hello, 世界! 🌍';
      const file = new File([content], 'test.txt', { type: 'text/plain' });
      
      const result = await FileValidator.readFileContent(file);
      
      expect(result).toBe(content);
    });

    it('should handle read errors gracefully', async () => {
      // Create a mock file that will cause a read error
      const mockFile = {
        slice: () => new Blob(),
        name: 'test.txt',
        size: 100,
        type: 'text/plain'
      } as File;

      await expect(FileValidator.readFileContent(mockFile)).rejects.toThrow();
    });
  });
});

describe('formatFileSize', () => {
  it('should format bytes correctly', () => {
    expect(formatFileSize(0)).toBe('0 Bytes');
    expect(formatFileSize(1024)).toBe('1 KB');
    expect(formatFileSize(1024 * 1024)).toBe('1 MB');
    expect(formatFileSize(1536)).toBe('1.5 KB');
  });
});

describe('hasFiles', () => {
  it('should detect files in drag event', () => {
    const mockEvent = {
      dataTransfer: {
        types: ['Files', 'text/plain']
      }
    } as DragEvent;
    
    expect(hasFiles(mockEvent)).toBe(true);
  });

  it('should return false when no files', () => {
    const mockEvent = {
      dataTransfer: {
        types: ['text/plain']
      }
    } as DragEvent;
    
    expect(hasFiles(mockEvent)).toBe(false);
  });

  it('should handle missing dataTransfer', () => {
    const mockEvent = {} as DragEvent;
    
    expect(hasFiles(mockEvent)).toBe(false);
  });
});

describe('getFilesFromDragEvent', () => {
  it('should extract files from drag event', () => {
    const file1 = new File(['content1'], 'file1.txt');
    const file2 = new File(['content2'], 'file2.txt');
    
    const mockEvent = {
      dataTransfer: {
        files: [file1, file2]
      }
    } as DragEvent;
    
    const files = getFilesFromDragEvent(mockEvent);
    
    expect(files).toHaveLength(2);
    expect(files[0]).toBe(file1);
    expect(files[1]).toBe(file2);
  });

  it('should return empty array when no files', () => {
    const mockEvent = {
      dataTransfer: {
        files: []
      }
    } as DragEvent;
    
    const files = getFilesFromDragEvent(mockEvent);
    
    expect(files).toHaveLength(0);
  });

  it('should handle missing dataTransfer', () => {
    const mockEvent = {} as DragEvent;
    
    const files = getFilesFromDragEvent(mockEvent);
    
    expect(files).toHaveLength(0);
  });
});
