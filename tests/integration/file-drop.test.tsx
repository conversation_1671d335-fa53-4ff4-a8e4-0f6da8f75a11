import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../../src/App';

describe('File Drop Integration', () => {
  beforeEach(() => {
    // Reset any global state
    localStorage.clear();
  });

  it('should load a markdown file and display it in the editor', async () => {
    render(<App />);
    
    // Verify we start with empty state
    expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
    
    // Create a test markdown file
    const markdownContent = '# Test Document\n\nThis is a test markdown file with **bold** text.';
    const file = new File([markdownContent], 'test.md', { type: 'text/markdown' });
    
    // Find the file input and upload the file
    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    await userEvent.upload(fileInput, file);
    
    // Wait for the file to be processed and editor to appear
    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
      expect(screen.getByText('Preview')).toBeInTheDocument();
    });
    
    // Verify the content is loaded in the editor
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue(markdownContent);

    // Verify the preview is rendered
    expect(screen.getByText('Test Document')).toBeInTheDocument();

    // Check that content appears in the preview (look for the preview container specifically)
    const previewContainer = document.querySelector('.preview-container');
    expect(previewContainer).toBeInTheDocument();
    expect(previewContainer?.textContent).toContain('This is a test markdown file with');
  });

  it('should handle text files correctly', async () => {
    render(<App />);
    
    const textContent = 'This is a plain text file.\nWith multiple lines.';
    const file = new File([textContent], 'test.txt', { type: 'text/plain' });
    
    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    await userEvent.upload(fileInput, file);
    
    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });
    
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue(textContent);
  });

  it('should reject invalid file types', async () => {
    render(<App />);
    
    const file = new File(['PDF content'], 'test.pdf', { type: 'application/pdf' });
    
    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;

    // Use fireEvent instead of userEvent for file input
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });
    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(screen.getByText(/Invalid file type/)).toBeInTheDocument();
    });

    // Should still show empty state
    expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
  });

  it('should handle empty files gracefully', async () => {
    render(<App />);

    const file = new File([''], 'empty.md', { type: 'text/markdown' });

    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    await userEvent.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText(/empty/)).toBeInTheDocument();
    });

    // Should still show empty state
    expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
  });

  it('should warn about large files and allow proceeding', async () => {
    render(<App />);

    // Create a file larger than 200KB
    const largeContent = '# Large File\n\n' + 'x'.repeat(250 * 1024);
    const file = new File([largeContent], 'large.md', { type: 'text/markdown' });

    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    await userEvent.upload(fileInput, file);
    
    // Should show warning dialog
    await waitFor(() => {
      expect(screen.getByText('Large File Warning')).toBeInTheDocument();
      expect(screen.getByText(/exceeds recommended limit/)).toBeInTheDocument();
    });
    
    // Click proceed
    fireEvent.click(screen.getByText('Proceed Anyway'));
    
    // Should load the file
    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });
    
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue(largeContent);
  });

  it('should cancel large file upload when user cancels', async () => {
    render(<App />);
    
    const largeContent = '# Large File\n\n' + 'x'.repeat(250 * 1024);
    const file = new File([largeContent], 'large.md', { type: 'text/markdown' });
    
    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    await userEvent.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText('Large File Warning')).toBeInTheDocument();
    });

    // Click cancel
    fireEvent.click(screen.getByText('Cancel'));

    // Should return to empty state
    await waitFor(() => {
      expect(screen.queryByText('Large File Warning')).not.toBeInTheDocument();
      expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
    });
  });

  it('should handle drag and drop operations', async () => {
    render(<App />);

    const markdownContent = '# Dragged File\n\nThis file was dragged and dropped.';
    const file = new File([markdownContent], 'dragged.md', { type: 'text/markdown' });

    const dropZone = screen.getByText('Drop markdown file here').closest('[class*="border-2"]')!;

    // Simulate drag enter
    fireEvent.dragEnter(dropZone, {
      dataTransfer: {
        types: ['Files'],
        files: [file]
      }
    });

    // Should show drag over state
    expect(dropZone).toHaveClass('border-primary-500');
    
    // Simulate drop
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file]
      }
    });
    
    // Should load the file
    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });
    
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue(markdownContent);
  });

  it('should handle multiple files by taking the first one', async () => {
    render(<App />);
    
    const file1 = new File(['# First File'], 'first.md', { type: 'text/markdown' });
    const file2 = new File(['# Second File'], 'second.md', { type: 'text/markdown' });
    
    const dropZone = screen.getByText('Drop markdown file here').closest('div')!;
    
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file1, file2]
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });
    
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue('# First File');
  });

  it('should create new document with default content', async () => {
    render(<App />);
    
    fireEvent.click(screen.getByText('New Document'));
    
    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });
    
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue('# Welcome to MDEdit\n\nStart typing your markdown here...');
    
    // Should show in preview too
    expect(screen.getByText('Welcome to MDEdit')).toBeInTheDocument();
  });

  it('should handle files with special characters and encoding', async () => {
    render(<App />);
    
    const specialContent = '# 特殊文字テスト\n\n这是中文测试 🌍 émojis and ñ special chars';
    const file = new File([specialContent], 'special.md', { type: 'text/markdown' });
    
    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    await userEvent.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByText('Editor')).toBeInTheDocument();
    });

    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue(specialContent);
  });

  it('should hide file drop zone when file is loaded', async () => {
    render(<App />);

    // Initially, file drop zone should be visible
    expect(screen.getByText('Drop markdown file here')).toBeInTheDocument();
    expect(screen.getByText('Choose File')).toBeInTheDocument();

    // Load a file
    const content = '# Test Document';
    const file = new File([content], 'test.md', { type: 'text/markdown' });

    const fileInput = screen.getByText('Choose File').closest('label')!.querySelector('input[type="file"]')!;
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });
    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    // File drop zone should no longer be visible
    expect(screen.queryByText('Drop markdown file here')).not.toBeInTheDocument();
    expect(screen.queryByText('Choose File')).not.toBeInTheDocument();

    // Editor should show the file content
    const editorTextarea = screen.getByRole('textbox');
    expect(editorTextarea).toHaveValue(content);
  });
});
