import { test, expect } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper function to create test files
const createTestFile = (filename: string, content: string) => {
  const filePath = path.join(__dirname, 'test-files', filename);
  fs.mkdirSync(path.dirname(filePath), { recursive: true });
  fs.writeFileSync(filePath, content, 'utf8');
  return filePath;
};

test.describe('MDEdit Application', () => {
  test('should load the application and show empty state', async ({ page }) => {
    // Load the built application directly
    const distPath = path.join(__dirname, '../../dist/index.html');
    await page.goto(`file://${distPath}`);

    // Check that the page loads
    await expect(page).toHaveTitle(/Vite \+ React \+ TS/);

    // Check header elements
    await expect(page.getByText('MDEdit')).toBeVisible();
    await expect(page.getByText('Epic 1')).toBeVisible();

    // Check empty state
    await expect(page.getByText('Drop markdown file here')).toBeVisible();
    await expect(page.getByText('Choose File')).toBeVisible();
    await expect(page.getByText('New Document')).toBeVisible();
  });

  test('should create a new document when clicking New Document', async ({ page }) => {
    const distPath = path.join(__dirname, '../../dist/index.html');
    await page.goto(`file://${distPath}`);

    // Click the New Document button
    await page.getByText('New Document').click();

    // Should show the editor and preview panes
    await expect(page.getByText('Editor')).toBeVisible();
    await expect(page.getByText('Preview')).toBeVisible();

    // Should show the default content
    await expect(page.getByText('Welcome to MDEdit')).toBeVisible();
  });

  test('should toggle theme when clicking theme button', async ({ page }) => {
    const distPath = path.join(__dirname, '../../dist/index.html');
    await page.goto(`file://${distPath}`);

    // Get the theme toggle button
    const themeToggle = page.getByLabel('Toggle theme');
    await expect(themeToggle).toBeVisible();

    // Click to toggle theme
    await themeToggle.click();

    // The theme should change (we can check for dark class on html element)
    const html = page.locator('html');
    await expect(html).toHaveClass(/dark/);
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    const distPath = path.join(__dirname, '../../dist/index.html');
    await page.goto(`file://${distPath}`);

    // Should still show main elements
    await expect(page.getByText('MDEdit')).toBeVisible();
    await expect(page.getByText('Drop markdown file here')).toBeVisible();
  });
});

test.describe('File Drag and Drop E2E', () => {
  test.beforeEach(async ({ page }) => {
    const distPath = path.join(__dirname, '../../dist/index.html');
    await page.goto(`file://${distPath}`);
  });

  test('should handle file upload via file input', async ({ page }) => {
    // Create a test markdown file
    const testContent = '# E2E Test Document\n\nThis is a test markdown file for E2E testing.';
    const testFilePath = createTestFile('test.md', testContent);

    // Upload file via file input
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Should show editor and preview
    await expect(page.getByText('Editor')).toBeVisible();
    await expect(page.getByText('Preview')).toBeVisible();

    // Should show content in editor
    const editor = page.getByRole('textbox');
    await expect(editor).toHaveValue(testContent);

    // Should show rendered content in preview
    await expect(page.getByText('E2E Test Document')).toBeVisible();
    await expect(page.getByText('This is a test markdown file for E2E testing.')).toBeVisible();

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should reject invalid file types', async ({ page }) => {
    // Create a test PDF file (simulated with .pdf extension)
    const testFilePath = createTestFile('test.pdf', 'PDF content');

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Should show error message
    await expect(page.getByText(/Invalid file type/)).toBeVisible();

    // Should still show empty state
    await expect(page.getByText('Drop markdown file here')).toBeVisible();

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should handle empty files', async ({ page }) => {
    const testFilePath = createTestFile('empty.md', '');

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Should show error message
    await expect(page.getByText(/empty/)).toBeVisible();

    // Should still show empty state
    await expect(page.getByText('Drop markdown file here')).toBeVisible();

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should show warning for large files and allow proceeding', async ({ page }) => {
    // Create a large file (over 200KB)
    const largeContent = '# Large File Test\n\n' + 'x'.repeat(250 * 1024);
    const testFilePath = createTestFile('large.md', largeContent);

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Should show warning dialog
    await expect(page.getByText('Large File Warning')).toBeVisible();
    await expect(page.getByText(/exceeds recommended limit/)).toBeVisible();

    // Click proceed
    await page.getByText('Proceed Anyway').click();

    // Should load the file
    await expect(page.getByText('Editor')).toBeVisible();
    const editor = page.getByRole('textbox');
    await expect(editor).toHaveValue(largeContent);

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should cancel large file upload', async ({ page }) => {
    const largeContent = '# Large File Test\n\n' + 'x'.repeat(250 * 1024);
    const testFilePath = createTestFile('large.md', largeContent);

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    await expect(page.getByText('Large File Warning')).toBeVisible();

    // Click cancel
    await page.getByText('Cancel').click();

    // Should return to empty state
    await expect(page.getByText('Drop markdown file here')).toBeVisible();
    await expect(page.getByText('Large File Warning')).not.toBeVisible();

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should handle files with special characters', async ({ page }) => {
    const specialContent = '# 特殊文字テスト\n\n这是中文测试 🌍 émojis and ñ special chars';
    const testFilePath = createTestFile('special.md', specialContent);

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    await expect(page.getByText('Editor')).toBeVisible();
    const editor = page.getByRole('textbox');
    await expect(editor).toHaveValue(specialContent);

    // Should render special characters in preview
    await expect(page.getByText('特殊文字テスト')).toBeVisible();

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should show loading state during file processing', async ({ page }) => {
    const testContent = '# Loading Test\n\nThis tests the loading state.';
    const testFilePath = createTestFile('loading-test.md', testContent);

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Loading state might be too fast to catch reliably, but we can verify the end state
    await expect(page.getByText('Editor')).toBeVisible();
    const editor = page.getByRole('textbox');
    await expect(editor).toHaveValue(testContent);

    // Cleanup
    fs.unlinkSync(testFilePath);
  });

  test('should replace current file when loading new one', async ({ page }) => {
    // Load first file
    const firstContent = '# First Document\n\nThis is the first document.';
    const firstFilePath = createTestFile('first.md', firstContent);

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(firstFilePath);

    await expect(page.getByText('Editor')).toBeVisible();
    let editor = page.getByRole('textbox');
    await expect(editor).toHaveValue(firstContent);

    // Load second file
    const secondContent = '# Second Document\n\nThis is the second document.';
    const secondFilePath = createTestFile('second.md', secondContent);

    await fileInput.setInputFiles(secondFilePath);

    // Should replace content
    editor = page.getByRole('textbox');
    await expect(editor).toHaveValue(secondContent);
    await expect(page.getByText('Second Document')).toBeVisible();

    // Cleanup
    fs.unlinkSync(firstFilePath);
    fs.unlinkSync(secondFilePath);
  });
});
