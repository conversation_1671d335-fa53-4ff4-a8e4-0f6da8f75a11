{"ci": {"collect": {"url": ["http://localhost:4173"], "startServerCommand": "npm run preview", "startServerReadyPattern": "Local:.*:4173"}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.9}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.9}], "first-contentful-paint": ["warn", {"maxNumericValue": 2000}], "largest-contentful-paint": ["warn", {"maxNumericValue": 2500}], "cumulative-layout-shift": ["warn", {"maxNumericValue": 0.1}]}}, "upload": {"target": "temporary-public-storage"}}}