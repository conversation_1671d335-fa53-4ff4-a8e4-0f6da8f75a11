{"name": "mdedit", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,md}\"", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@headlessui/react": "^2.2.7", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "marked": "^9.1.6", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^3.4.17", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@playwright/test": "^1.55.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@vitejs/plugin-react": "^5.0.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vite-plugin-pwa": "^1.0.3", "vitest": "^0.34.6"}}