import { useState } from 'react';
import { FileDropZone } from '../file/FileDropZone';
import { BasicEditor } from '../editor/BasicEditor';
import { PreviewPane } from '../preview/PreviewPane';
import { useResponsiveLayout } from '../../hooks/useResponsiveLayout';
import type { FileData, EpicLevel } from '../../types/editor';

interface MainEditorProps {
  currentFile: FileData | null;
  onFileLoad: (file: FileData) => void;
  onFileChange: (content: string) => void;
  epic: EpicLevel;
}

type ActivePane = 'editor' | 'preview';

export function MainEditor({
  currentFile,
  onFileLoad,
  onFileChange,
  epic: _epic
}: MainEditorProps) {
  const layout = useResponsiveLayout();
  const [activePane, setActivePane] = useState<ActivePane>('editor');

  if (!currentFile) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <FileDropZone onFileLoad={onFileLoad} />
      </div>
    );
  }

  // Mobile layout: stacked with tab switching
  if (layout.isMobile) {
    return (
      <div className="flex-1 flex flex-col">
        {/* Mobile Tab Bar */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <button
            onClick={() => setActivePane('editor')}
            className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
              activePane === 'editor'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          >
            Editor
          </button>
          <button
            onClick={() => setActivePane('preview')}
            className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
              activePane === 'preview'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          >
            Preview
          </button>
        </div>

        {/* Mobile Content */}
        <div className="flex-1 overflow-hidden">
          {activePane === 'editor' ? (
            <BasicEditor
              content={currentFile.content}
              onChange={onFileChange}
              placeholder="Start typing your markdown..."
            />
          ) : (
            <PreviewPane content={currentFile.content} autoUpdate={false} />
          )}
        </div>
      </div>
    );
  }

  // Tablet layout: tabbed interface with full-width panes
  if (layout.isTablet) {
    return (
      <div className="flex-1 flex flex-col">
        {/* Tablet Tab Bar */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <button
            onClick={() => setActivePane('editor')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activePane === 'editor'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          >
            📝 Editor
          </button>
          <button
            onClick={() => setActivePane('preview')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activePane === 'preview'
                ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-600 dark:border-primary-400 bg-white dark:bg-gray-900'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
            }`}
          >
            👁️ Preview
          </button>
        </div>

        {/* Tablet Content */}
        <div className="flex-1 overflow-hidden">
          {activePane === 'editor' ? (
            <BasicEditor
              content={currentFile.content}
              onChange={onFileChange}
              placeholder="Start typing your markdown..."
            />
          ) : (
            <PreviewPane content={currentFile.content} autoUpdate={false} />
          )}
        </div>
      </div>
    );
  }

  // Desktop layout: 50/50 split pane
  return (
    <div className="flex-1 flex">
      {/* Editor Pane */}
      <div className="flex-1 border-r border-gray-200 dark:border-gray-700">
        <BasicEditor
          content={currentFile.content}
          onChange={onFileChange}
          placeholder="Start typing your markdown..."
        />
      </div>

      {/* Preview Pane */}
      <div className="flex-1">
        <PreviewPane content={currentFile.content} autoUpdate={false} />
      </div>
    </div>
  );
}
