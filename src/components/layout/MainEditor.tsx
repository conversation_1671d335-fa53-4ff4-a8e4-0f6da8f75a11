import { FileDropZone } from '../file/FileDropZone';
import { BasicEditor } from '../editor/BasicEditor';
import { PreviewPane } from '../preview/PreviewPane';
import type { FileData, EpicLevel } from '../../types/editor';

interface MainEditorProps {
  currentFile: FileData | null;
  onFileLoad: (file: FileData) => void;
  onFileChange: (content: string) => void;
  epic: EpicLevel;
}

export function MainEditor({
  currentFile,
  onFileLoad,
  onFileChange,
  epic: _epic
}: MainEditorProps) {
  if (!currentFile) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <FileDropZone onFileLoad={onFileLoad} />
      </div>
    );
  }

  return (
    <div className="flex-1 flex">
      {/* Editor Pane */}
      <div className="flex-1 border-r border-gray-200 dark:border-gray-700">
        <BasicEditor
          content={currentFile.content}
          onChange={onFileChange}
          placeholder="Start typing your markdown..."
        />
      </div>

      {/* Preview Pane */}
      <div className="flex-1">
        <PreviewPane content={currentFile.content} />
      </div>
    </div>
  );
}
