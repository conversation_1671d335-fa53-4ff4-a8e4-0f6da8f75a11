import { useCallback, useRef, useEffect, useState } from 'react';
import type { EditorProps } from '../../types/editor';
import { useUndoRedo } from '../../hooks/useUndoRedo';
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts';

export function BasicEditor({
  content,
  onChange,
  placeholder = "Start typing your markdown...",
  disabled = false
}: EditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [localContent, undoRedoActions] = useUndoRedo(content);
  const [isPerformanceMode, setIsPerformanceMode] = useState(false);

  // Sync external content changes with undo/redo state
  useEffect(() => {
    if (content !== localContent) {
      undoRedoActions.set(content);
    }
  }, [content, localContent, undoRedoActions]);

  // Performance monitoring for large files
  useEffect(() => {
    const shouldUsePerformanceMode = content.length > 50000; // 50KB threshold
    setIsPerformanceMode(shouldUsePerformanceMode);
  }, [content.length]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    undoRedoActions.set(newValue);
    onChange(newValue);
  }, [onChange, undoRedoActions]);

  const handleSelectAll = useCallback(() => {
    if (textareaRef.current) {
      textareaRef.current.select();
    }
  }, []);

  const handleTab = useCallback((e: KeyboardEvent) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const spaces = '  '; // 2 spaces for markdown

    // Insert spaces at cursor position
    const newValue =
      localContent.substring(0, start) +
      spaces +
      localContent.substring(end);

    undoRedoActions.set(newValue);
    onChange(newValue);

    // Move cursor after the inserted spaces
    setTimeout(() => {
      if (textarea) {
        textarea.selectionStart = textarea.selectionEnd = start + spaces.length;
        textarea.focus();
      }
    }, 0);
  }, [localContent, onChange, undoRedoActions]);

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    onUndo: undoRedoActions.undo,
    onRedo: undoRedoActions.redo,
    onSelectAll: handleSelectAll,
    onTab: handleTab
  }, !disabled);

  return (
    <div className="h-full flex flex-col">
      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Editor
          </h2>
          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
            {isPerformanceMode && (
              <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">
                Large File
              </span>
            )}
            <span>{content.length.toLocaleString()} chars</span>
          </div>
        </div>
      </div>

      <textarea
        ref={textareaRef}
        value={localContent}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className="editor-container flex-1 resize-none border-0 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20"
        spellCheck={false}
        autoComplete="off"
        autoCorrect="off"
        autoCapitalize="off"
        data-gramm="false"
        style={{
          lineHeight: '1.5',
          fontFamily: 'JetBrains Mono, Fira Code, monospace',
          fontSize: '14px',
          tabSize: 2
        }}
      />
    </div>
  );
}
