import { useCallback } from 'react';
import type { EditorProps } from '../../types/editor';

export function BasicEditor({ 
  content, 
  onChange, 
  placeholder = "Start typing your markdown...",
  disabled = false 
}: EditorProps) {
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  return (
    <div className="h-full flex flex-col">
      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <h2 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Editor
        </h2>
      </div>
      
      <textarea
        value={content}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className="editor-container flex-1 resize-none border-0 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none"
        spellCheck={false}
        autoComplete="off"
        autoCorrect="off"
        autoCapitalize="off"
        data-gramm="false"
      />
    </div>
  );
}
