import { useState, useCallback, useEffect } from 'react';
import { MarkdownPars<PERSON>, ParseResult } from '../../services/MarkdownParser';

interface PreviewPaneProps {
  content: string;
  autoUpdate?: boolean; // For Epic 2+, defaults to false for Epic 1
}

export function PreviewPane({ content, autoUpdate = false }: PreviewPaneProps) {
  const [parseResult, setParseResult] = useState<ParseResult>({ html: '', processingTime: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Track if content has changed since last update
  useEffect(() => {
    if (lastUpdated && content !== parseResult.html) {
      setHasChanges(true);
    }
  }, [content, parseResult.html, lastUpdated]);

  const updatePreview = useCallback(async () => {
    setIsLoading(true);
    setHasChanges(false);

    try {
      const result = await MarkdownParser.parseAsync(content);
      setParseResult(result);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Preview update error:', error);
      setParseResult({
        html: '<div class="error-preview">Failed to update preview</div>',
        error: error instanceof Error ? error.message : String(error),
        processingTime: 0
      });
    } finally {
      setIsLoading(false);
    }
  }, [content]);

  // Auto-update for Epic 2+ or initial load
  useEffect(() => {
    if (autoUpdate || !lastUpdated) {
      updatePreview();
    }
  }, [content, autoUpdate, updatePreview, lastUpdated]);

  const isLargeContent = MarkdownParser.isLargeContent(content);
  const estimatedTime = MarkdownParser.estimateProcessingTime(content);

  return (
    <div className="h-full flex flex-col">
      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Preview
          </h2>

          <div className="flex items-center space-x-2">
            {/* Performance indicator for large content */}
            {isLargeContent && (
              <span className="px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">
                Large Content (~{estimatedTime.toFixed(0)}ms)
              </span>
            )}

            {/* Update status */}
            {lastUpdated && (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {hasChanges ? 'Changes pending' : `Updated ${lastUpdated.toLocaleTimeString()}`}
              </span>
            )}

            {/* Manual update button for Epic 1 */}
            {!autoUpdate && (
              <button
                onClick={updatePreview}
                disabled={isLoading}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  hasChanges
                    ? 'bg-blue-500 hover:bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {isLoading ? 'Updating...' : hasChanges ? 'Update Preview' : 'Refresh'}
              </button>
            )}
          </div>
        </div>

        {/* Processing time indicator */}
        {parseResult.processingTime > 0 && (
          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Processed in {parseResult.processingTime.toFixed(1)}ms
            {parseResult.error && (
              <span className="ml-2 text-red-500">• Error: {parseResult.error}</span>
            )}
          </div>
        )}
      </div>

      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              <span className="text-sm">Rendering preview...</span>
            </div>
          </div>
        ) : (
          <div
            className="preview-container markdown-body p-4"
            dangerouslySetInnerHTML={{ __html: parseResult.html }}
          />
        )}
      </div>
    </div>
  );
}
