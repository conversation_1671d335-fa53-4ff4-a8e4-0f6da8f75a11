import { useMemo } from 'react';
import { marked } from 'marked';

interface PreviewPaneProps {
  content: string;
}

export function PreviewPane({ content }: PreviewPaneProps) {
  const htmlContent = useMemo(() => {
    if (!content.trim()) {
      return '<p class="text-gray-500 italic">Preview will appear here...</p>';
    }

    try {
      // Configure marked for GitHub Flavored Markdown
      marked.setOptions({
        gfm: true,
        breaks: true,
      });

      return marked(content);
    } catch (error) {
      console.error('Markdown parsing error:', error);
      return '<p class="text-red-500">Error parsing markdown</p>';
    }
  }, [content]);

  return (
    <div className="h-full flex flex-col">
      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <h2 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Preview
        </h2>
      </div>
      
      <div className="flex-1 overflow-auto">
        <div 
          className="preview-container prose prose-sm max-w-none p-4 dark:prose-invert"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </div>
    </div>
  );
}
