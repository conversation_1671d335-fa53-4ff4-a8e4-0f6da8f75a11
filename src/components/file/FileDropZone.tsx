import { useState, useCallback } from 'react';
import type { FileData, DropZoneState } from '../../types/file';
import { FileValidator, formatFileSize, hasFiles, getFilesFromDragEvent } from '../../utils/file-utils';

interface FileDropZoneProps {
  onFileLoad: (file: FileData) => void;
  onError?: (error: string) => void;
  maxSize?: number;
  acceptedTypes?: string[];
  className?: string;
}

export function FileDropZone({
  onFileLoad,
  onError,
  maxSize = 200 * 1024, // 200KB default
  acceptedTypes = ['.md', '.markdown', '.txt'],
  className = ''
}: FileDropZoneProps) {
  const [dropState, setDropState] = useState<DropZoneState>({
    isDragOver: false,
    isProcessing: false,
  });
  const [showSizeWarning, setShowSizeWarning] = useState<{
    show: boolean;
    file: File | null;
    warnings: string[];
  }>({
    show: false,
    file: null,
    warnings: []
  });

  const processFile = useCallback(async (file: File, skipWarnings = false) => {
    // Validate the file
    const validation = FileValidator.validate(file, {
      maxSize,
      acceptedExtensions: acceptedTypes
    });

    if (!validation.isValid) {
      const errorMessage = validation.error || 'Invalid file';
      setDropState(prev => ({ ...prev, error: errorMessage }));
      onError?.(errorMessage);
      return;
    }

    // Handle size warnings
    if (validation.warnings && validation.warnings.length > 0 && !skipWarnings) {
      setShowSizeWarning({
        show: true,
        file,
        warnings: validation.warnings
      });
      return;
    }

    setDropState(prev => ({ ...prev, isProcessing: true, error: undefined }));
    setShowSizeWarning({ show: false, file: null, warnings: [] });

    try {
      // Use the enhanced file reader with encoding detection
      const content = await FileValidator.readFileContent(file);

      const fileData: FileData = {
        id: crypto.randomUUID(),
        name: file.name,
        content,
        size: file.size,
        lastModified: file.lastModified,
        type: file.type,
      };

      onFileLoad(fileData);
      setDropState(prev => ({ ...prev, error: undefined }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to read file. Please try again.';
      setDropState(prev => ({
        ...prev,
        error: errorMessage
      }));
      onError?.(errorMessage);
    } finally {
      setDropState(prev => ({ ...prev, isProcessing: false }));
    }
  }, [onFileLoad, onError, maxSize, acceptedTypes]);

  const handleProceedWithWarning = useCallback(() => {
    if (showSizeWarning.file) {
      processFile(showSizeWarning.file, true);
    }
  }, [showSizeWarning.file, processFile]);

  const handleCancelWarning = useCallback(() => {
    setShowSizeWarning({ show: false, file: null, warnings: [] });
  }, []);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (hasFiles(e.nativeEvent)) {
      setDropState(prev => ({ ...prev, isDragOver: true }));
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (hasFiles(e.nativeEvent)) {
      setDropState(prev => ({ ...prev, isDragOver: true }));
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Only hide drag state if we're leaving the drop zone entirely
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDropState(prev => ({ ...prev, isDragOver: false }));
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDropState(prev => ({ ...prev, isDragOver: false }));

    const files = getFilesFromDragEvent(e.nativeEvent);
    if (files.length > 0) {
      // Handle multiple files by taking the first one (AC 8: replace current content)
      processFile(files[0]);
    }
  }, [processFile]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
    // Clear the input value to allow selecting the same file again
    e.target.value = '';
  }, [processFile]);

  return (
    <div className={`w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 ${className}`}>
      <div
        className={`
          relative min-h-[400px] sm:min-h-[500px] lg:min-h-[600px]
          border-2 border-dashed rounded-xl p-8 sm:p-12 lg:p-16
          text-center transition-all duration-300 ease-in-out
          ${dropState.isDragOver
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900 dark:bg-opacity-20 scale-[1.02] shadow-lg'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
          ${dropState.isProcessing ? 'opacity-50 pointer-events-none' : 'cursor-pointer'}
          bg-white dark:bg-gray-900
        `}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="space-y-6 sm:space-y-8">
          {/* Icon */}
          <div className="mx-auto w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-gray-400 dark:text-gray-500">
            <svg
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              className={`w-full h-full transition-transform duration-300 ${
                dropState.isDragOver ? 'scale-110' : ''
              }`}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
          </div>

          {/* Main Content */}
          <div className="text-center space-y-4">
            <h3 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white">
              Drop markdown file here
            </h3>
            <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              Start editing by dropping a .md file or create a new document
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <label className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer transition-all duration-200 hover:scale-105 shadow-lg">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 12l2 2 4-4" />
                </svg>
                Choose File
                <input
                  type="file"
                  className="sr-only"
                  accept={acceptedTypes.join(',')}
                  onChange={handleFileSelect}
                  disabled={dropState.isProcessing}
                />
              </label>

              <span className="text-gray-500 dark:text-gray-400">or</span>

              <button
                className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 hover:scale-105"
                onClick={() => {
                  const newFile: FileData = {
                    id: crypto.randomUUID(),
                    name: 'untitled.md',
                    content: '# Welcome to MDEdit\n\nStart typing your markdown here...',
                    size: 0,
                    lastModified: Date.now(),
                    type: 'text/markdown',
                  };
                  onFileLoad(newFile);
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                New Document
              </button>
            </div>
          </div>

          {/* Processing State */}
          {dropState.isProcessing && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 rounded-xl">
              <div className="text-center space-y-4">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  Processing file...
                </p>
              </div>
            </div>
          )}

          {/* Error State */}
          {dropState.error && (
            <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  {dropState.error}
                </p>
              </div>
            </div>
          )}

          {/* File Support Info */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-wrap justify-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {acceptedTypes.join(', ')}
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Recommended: {formatFileSize(maxSize)}
              </span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                UTF-8, UTF-16 support
              </span>
            </div>
          </div>
        </div>

        {/* Size Warning Modal */}
        {showSizeWarning.show && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-xl z-10">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 shadow-xl">
              <div className="flex items-center mb-4">
                <svg className="w-6 h-6 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Large File Warning
                </h3>
              </div>

              <div className="mb-4 space-y-2">
                {showSizeWarning.warnings.map((warning, index) => (
                  <p key={index} className="text-sm text-gray-600 dark:text-gray-300">
                    {warning}
                  </p>
                ))}
              </div>

              <div className="flex gap-3 justify-end">
                <button
                  onClick={handleCancelWarning}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleProceedWithWarning}
                  className="px-4 py-2 text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                >
                  Proceed Anyway
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
