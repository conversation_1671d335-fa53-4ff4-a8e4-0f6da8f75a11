import type { FileValidationResult } from '../types/file';

export interface FileValidationOptions {
  maxSize?: number;
  acceptedTypes?: string[];
  acceptedExtensions?: string[];
}

export interface EncodingDetectionResult {
  encoding: string;
  confidence: number;
}

export class FileValidator {
  private static readonly DEFAULT_MAX_SIZE = 200 * 1024; // 200KB
  private static readonly DEFAULT_ACCEPTED_EXTENSIONS = ['.md', '.markdown', '.txt'];
  private static readonly DEFAULT_ACCEPTED_TYPES = [
    'text/markdown',
    'text/plain',
    'application/octet-stream' // fallback for files without proper MIME type
  ];

  /**
   * Validates a file against the specified criteria
   */
  static validate(
    file: File,
    options: FileValidationOptions = {}
  ): FileValidationResult {
    const {
      maxSize = this.DEFAULT_MAX_SIZE,
      acceptedTypes = this.DEFAULT_ACCEPTED_TYPES,
      acceptedExtensions = this.DEFAULT_ACCEPTED_EXTENSIONS
    } = options;

    const warnings: string[] = [];
    let isValid = true;
    let error: string | undefined;

    // Check if file is empty
    if (file.size === 0) {
      return {
        isValid: false,
        error: 'The selected file is empty. Please choose a file with content.'
      };
    }

    // Check file extension
    const fileExtension = this.getFileExtension(file.name);
    const hasValidExtension = acceptedExtensions.some(ext => 
      fileExtension.toLowerCase() === ext.toLowerCase()
    );

    if (!hasValidExtension) {
      return {
        isValid: false,
        error: `Invalid file type. Please select a file with one of these extensions: ${acceptedExtensions.join(', ')}`
      };
    }

    // Check MIME type (with fallback for files that might not have proper MIME type)
    const hasValidMimeType = acceptedTypes.some(type => 
      file.type === type || file.type.startsWith('text/')
    );

    if (!hasValidMimeType && file.type !== '') {
      warnings.push(`File MIME type "${file.type}" is not recognized as a text file. Proceeding based on file extension.`);
    }

    // Check file size
    if (file.size > maxSize) {
      const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(2);
      
      warnings.push(
        `File size (${sizeMB}MB) exceeds recommended limit of ${maxSizeMB}MB. Large files may impact performance.`
      );
    }

    return {
      isValid,
      error,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Detects the character encoding of a file
   */
  static async detectEncoding(file: File): Promise<EncodingDetectionResult> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      
      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer;
        const uint8Array = new Uint8Array(arrayBuffer);
        
        // Check for BOM (Byte Order Mark)
        if (uint8Array.length >= 3) {
          // UTF-8 BOM
          if (uint8Array[0] === 0xEF && uint8Array[1] === 0xBB && uint8Array[2] === 0xBF) {
            resolve({ encoding: 'UTF-8', confidence: 1.0 });
            return;
          }
        }
        
        if (uint8Array.length >= 2) {
          // UTF-16 LE BOM
          if (uint8Array[0] === 0xFF && uint8Array[1] === 0xFE) {
            resolve({ encoding: 'UTF-16LE', confidence: 1.0 });
            return;
          }
          
          // UTF-16 BE BOM
          if (uint8Array[0] === 0xFE && uint8Array[1] === 0xFF) {
            resolve({ encoding: 'UTF-16BE', confidence: 1.0 });
            return;
          }
        }

        // Heuristic detection for UTF-8 vs other encodings
        let utf8Confidence = this.calculateUTF8Confidence(uint8Array);
        
        if (utf8Confidence > 0.8) {
          resolve({ encoding: 'UTF-8', confidence: utf8Confidence });
        } else {
          // Default to UTF-8 for text files
          resolve({ encoding: 'UTF-8', confidence: 0.5 });
        }
      };

      reader.onerror = () => {
        resolve({ encoding: 'UTF-8', confidence: 0.1 });
      };

      // Read first 1KB for encoding detection
      const blob = file.slice(0, 1024);
      reader.readAsArrayBuffer(blob);
    });
  }

  /**
   * Reads file content with proper encoding handling
   */
  static async readFileContent(file: File): Promise<string> {
    const encodingResult = await this.detectEncoding(file);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = () => {
        resolve(reader.result as string);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file content'));
      };

      // Use detected encoding or fallback to UTF-8
      if (encodingResult.encoding === 'UTF-16LE' || encodingResult.encoding === 'UTF-16BE') {
        reader.readAsText(file, encodingResult.encoding);
      } else {
        reader.readAsText(file, 'UTF-8');
      }
    });
  }

  /**
   * Gets file extension from filename
   */
  private static getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex === -1 ? '' : filename.substring(lastDotIndex);
  }

  /**
   * Calculates confidence that the byte array is UTF-8 encoded
   */
  private static calculateUTF8Confidence(bytes: Uint8Array): number {
    let validUTF8Sequences = 0;
    let totalSequences = 0;
    
    for (let i = 0; i < bytes.length; i++) {
      const byte = bytes[i];
      
      // ASCII characters (0-127) are valid UTF-8
      if (byte <= 0x7F) {
        validUTF8Sequences++;
        totalSequences++;
        continue;
      }
      
      // Multi-byte UTF-8 sequences
      let sequenceLength = 0;
      if ((byte & 0xE0) === 0xC0) sequenceLength = 2;
      else if ((byte & 0xF0) === 0xE0) sequenceLength = 3;
      else if ((byte & 0xF8) === 0xF0) sequenceLength = 4;
      
      if (sequenceLength > 0 && i + sequenceLength <= bytes.length) {
        let validSequence = true;
        for (let j = 1; j < sequenceLength; j++) {
          if ((bytes[i + j] & 0xC0) !== 0x80) {
            validSequence = false;
            break;
          }
        }
        
        if (validSequence) {
          validUTF8Sequences++;
        }
        totalSequences++;
        i += sequenceLength - 1;
      } else {
        totalSequences++;
      }
    }
    
    return totalSequences > 0 ? validUTF8Sequences / totalSequences : 0;
  }
}

/**
 * Utility function to format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Utility function to check if drag event contains files
 */
export function hasFiles(event: DragEvent): boolean {
  return event.dataTransfer?.types.includes('Files') ?? false;
}

/**
 * Utility function to extract files from drag event
 */
export function getFilesFromDragEvent(event: DragEvent): File[] {
  const files: File[] = [];
  
  if (event.dataTransfer?.files) {
    for (let i = 0; i < event.dataTransfer.files.length; i++) {
      const file = event.dataTransfer.files[i];
      if (file) {
        files.push(file);
      }
    }
  }
  
  return files;
}
