/* GitHub-compatible Markdown Styles */
.markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 16px;
  line-height: 1.5;
  color: #1f2328;
  background-color: #ffffff;
}

.dark .markdown-body {
  color: #e6edf3;
  background-color: #0d1117;
}

/* Headings */
.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 {
  font-size: 2em;
  border-bottom: 1px solid #d1d9e0;
  padding-bottom: 0.3em;
}

.dark .markdown-body h1 {
  border-bottom-color: #30363d;
}

.markdown-body h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #d1d9e0;
  padding-bottom: 0.3em;
}

.dark .markdown-body h2 {
  border-bottom-color: #30363d;
}

.markdown-body h3 {
  font-size: 1.25em;
}

.markdown-body h4 {
  font-size: 1em;
}

.markdown-body h5 {
  font-size: 0.875em;
}

.markdown-body h6 {
  font-size: 0.85em;
  color: #656d76;
}

.dark .markdown-body h6 {
  color: #7d8590;
}

/* Paragraphs */
.markdown-body p {
  margin-top: 0;
  margin-bottom: 16px;
}

/* Links */
.markdown-body .markdown-link {
  color: #0969da;
  text-decoration: none;
}

.markdown-body .markdown-link:hover {
  text-decoration: underline;
}

.dark .markdown-body .markdown-link {
  color: #58a6ff;
}

/* Code */
.markdown-body .inline-code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  background-color: #afb8c133;
  border-radius: 6px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.dark .markdown-body .inline-code {
  background-color: #6e768166;
}

.markdown-body .code-block {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.dark .markdown-body .code-block {
  background-color: #161b22;
}

.markdown-body .code-block code {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* Lists */
.markdown-body ul,
.markdown-body ol {
  margin-top: 0;
  margin-bottom: 16px;
  padding-left: 2em;
}

.markdown-body li {
  margin-bottom: 0.25em;
}

.markdown-body li > p {
  margin-top: 16px;
}

.markdown-body li + li {
  margin-top: 0.25em;
}

/* Blockquotes */
.markdown-body .markdown-blockquote {
  padding: 0 1em;
  color: #656d76;
  border-left: 0.25em solid #d1d9e0;
  margin: 0 0 16px 0;
}

.dark .markdown-body .markdown-blockquote {
  color: #7d8590;
  border-left-color: #30363d;
}

.markdown-body .markdown-blockquote > :first-child {
  margin-top: 0;
}

.markdown-body .markdown-blockquote > :last-child {
  margin-bottom: 0;
}

/* Tables */
.markdown-body .table-wrapper {
  overflow-x: auto;
  margin-bottom: 16px;
}

.markdown-body .markdown-table {
  border-spacing: 0;
  border-collapse: collapse;
  display: table;
  width: max-content;
  max-width: 100%;
  overflow: auto;
}

.markdown-body .markdown-table th,
.markdown-body .markdown-table td {
  padding: 6px 13px;
  border: 1px solid #d1d9e0;
}

.dark .markdown-body .markdown-table th,
.dark .markdown-body .markdown-table td {
  border-color: #30363d;
}

.markdown-body .markdown-table th {
  font-weight: 600;
  background-color: #f6f8fa;
}

.dark .markdown-body .markdown-table th {
  background-color: #161b22;
}

.markdown-body .markdown-table tr {
  background-color: #ffffff;
  border-top: 1px solid #d1d9e0;
}

.dark .markdown-body .markdown-table tr {
  background-color: #0d1117;
  border-top-color: #30363d;
}

.markdown-body .markdown-table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.dark .markdown-body .markdown-table tr:nth-child(2n) {
  background-color: #161b22;
}

/* Images */
.markdown-body .markdown-image {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 16px 0;
}

.markdown-body .image-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background-color: #f6f8fa;
  border: 1px dashed #d1d9e0;
  border-radius: 6px;
  margin: 16px 0;
}

.dark .markdown-body .image-fallback {
  background-color: #161b22;
  border-color: #30363d;
}

.markdown-body .image-fallback-text {
  font-size: 14px;
  color: #656d76;
  margin-bottom: 4px;
}

.dark .markdown-body .image-fallback-text {
  color: #7d8590;
}

.markdown-body .image-fallback-url {
  font-size: 12px;
  color: #8c959f;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  word-break: break-all;
}

.dark .markdown-body .image-fallback-url {
  color: #6e7681;
}

/* Special states */
.markdown-body .empty-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #656d76;
  font-style: italic;
  font-size: 14px;
}

.dark .markdown-body .empty-preview {
  color: #7d8590;
}

.markdown-body .error-preview {
  padding: 16px;
  background-color: #fff8f0;
  border: 1px solid #fd8c73;
  border-radius: 6px;
  color: #d1242f;
  margin: 16px 0;
}

.dark .markdown-body .error-preview {
  background-color: #2d1b1b;
  border-color: #f85149;
  color: #f85149;
}

.markdown-body .error-preview h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
}

.markdown-body .error-preview details {
  margin-top: 8px;
}

.markdown-body .error-preview summary {
  cursor: pointer;
  font-weight: 600;
}

.markdown-body .error-preview pre {
  background-color: #f6f8fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 8px;
  overflow-x: auto;
}

.dark .markdown-body .error-preview pre {
  background-color: #161b22;
}

/* Horizontal rules */
.markdown-body hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #d1d9e0;
  border: 0;
}

.dark .markdown-body hr {
  background-color: #30363d;
}
