import { useState, useEffect } from 'react';
import { Header } from './components/layout/Header';
import { MainEditor } from './components/layout/MainEditor';
import type { FileData, ThemeMode, EpicLevel } from './types/editor';

// Progressive Epic Detection
const detectAvailableEpic = (): EpicLevel => {
  if (typeof Worker !== 'undefined' && 'serviceWorker' in navigator) return 4;
  if ((window as any).CodeMirror || import.meta.env.VITE_EPIC_LEVEL >= 3) return 3;
  if (import.meta.env.VITE_EPIC_LEVEL >= 2) return 2;
  return 1;
};

interface AppProps {
  initialTheme?: ThemeMode;
}

function App({ initialTheme = 'system' }: AppProps) {
  const [currentFile, setCurrentFile] = useState<FileData | null>(null);
  const [theme, setTheme] = useState<ThemeMode>(initialTheme);
  const [epic] = useState<EpicLevel>(detectAvailableEpic());
  const [isOffline] = useState(false);

  // Apply theme to document
  useEffect(() => {
    const root = window.document.documentElement;

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.toggle('dark', systemTheme === 'dark');
    } else {
      root.classList.toggle('dark', theme === 'dark');
    }
  }, [theme]);

  const handleFileLoad = (file: FileData) => {
    setCurrentFile(file);
  };

  const handleFileChange = (content: string) => {
    if (currentFile) {
      setCurrentFile({
        ...currentFile,
        content,
        lastModified: Date.now(),
      });
    }
  };

  return (
    <div className="h-screen flex flex-col bg-white dark:bg-gray-900">
      <Header
        theme={theme}
        onThemeChange={setTheme}
        currentFile={currentFile}
        epic={epic}
        isOffline={isOffline}
      />
      <MainEditor
        currentFile={currentFile}
        onFileLoad={handleFileLoad}
        onFileChange={handleFileChange}
        epic={epic}
      />
    </div>
  );
}

export default App;
