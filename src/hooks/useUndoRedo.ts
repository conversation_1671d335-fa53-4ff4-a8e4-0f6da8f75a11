import { useState, useCallback, useRef } from 'react';

interface UndoRedoState {
  past: string[];
  present: string;
  future: string[];
}

interface UndoRedoActions {
  undo: () => void;
  redo: () => void;
  set: (newValue: string) => void;
  canUndo: boolean;
  canRedo: boolean;
}

const MAX_HISTORY_SIZE = 50;

export function useUndoRedo(initialValue: string = ''): [string, UndoRedoActions] {
  const [state, setState] = useState<UndoRedoState>({
    past: [],
    present: initialValue,
    future: []
  });

  const lastSaveTime = useRef<number>(Date.now());
  const saveThreshold = 1000; // Save to history every 1 second of inactivity

  const undo = useCallback(() => {
    setState(currentState => {
      if (currentState.past.length === 0) return currentState;

      const previous = currentState.past[currentState.past.length - 1];
      const newPast = currentState.past.slice(0, currentState.past.length - 1);

      return {
        past: newPast,
        present: previous,
        future: [currentState.present, ...currentState.future]
      };
    });
  }, []);

  const redo = useCallback(() => {
    setState(currentState => {
      if (currentState.future.length === 0) return currentState;

      const next = currentState.future[0];
      const newFuture = currentState.future.slice(1);

      return {
        past: [...currentState.past, currentState.present],
        present: next,
        future: newFuture
      };
    });
  }, []);

  const set = useCallback((newValue: string) => {
    const now = Date.now();
    const shouldSaveToHistory = now - lastSaveTime.current > saveThreshold;

    setState(currentState => {
      // If the value hasn't changed, don't update
      if (currentState.present === newValue) return currentState;

      // If we should save to history (based on time threshold)
      if (shouldSaveToHistory && currentState.present !== newValue) {
        lastSaveTime.current = now;
        
        const newPast = [...currentState.past, currentState.present];
        
        // Limit history size
        const trimmedPast = newPast.length > MAX_HISTORY_SIZE 
          ? newPast.slice(-MAX_HISTORY_SIZE)
          : newPast;

        return {
          past: trimmedPast,
          present: newValue,
          future: [] // Clear future when new content is added
        };
      }

      // Just update the present value without saving to history
      return {
        ...currentState,
        present: newValue
      };
    });
  }, [saveThreshold]);

  const actions: UndoRedoActions = {
    undo,
    redo,
    set,
    canUndo: state.past.length > 0,
    canRedo: state.future.length > 0
  };

  return [state.present, actions];
}
