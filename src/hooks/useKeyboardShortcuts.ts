import { useEffect, useCallback } from 'react';

interface KeyboardShortcutHandlers {
  onUndo?: () => void;
  onRedo?: () => void;
  onSelectAll?: () => void;
  onTab?: (e: KeyboardEvent) => void;
}

// Detect if we're on Mac for Cmd vs Ctrl
const isMac = typeof navigator !== 'undefined' && 
  navigator.platform.toUpperCase().indexOf('MAC') >= 0;

export function useKeyboardShortcuts(
  handlers: KeyboardShortcutHandlers,
  enabled: boolean = true
) {
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!enabled) return;

    const cmdKey = isMac ? e.metaKey : e.ctrlKey;
    
    // Handle Ctrl/Cmd + Z (Undo)
    if (cmdKey && e.key === 'z' && !e.shiftKey) {
      e.preventDefault();
      handlers.onUndo?.();
      return;
    }

    // Handle Ctrl/Cmd + Y (Redo) or Ctrl/Cmd + Shift + Z (Redo on Mac)
    if (cmdKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
      e.preventDefault();
      handlers.onRedo?.();
      return;
    }

    // Handle Ctrl/Cmd + A (Select All)
    if (cmdKey && e.key === 'a') {
      e.preventDefault();
      handlers.onSelectAll?.();
      return;
    }

    // Handle Tab key for markdown indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      handlers.onTab?.(e);
      return;
    }
  }, [handlers, enabled]);

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, enabled]);

  return { isMac };
}
