@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-primary-50: #f0f9ff;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
  }

  * {
    @apply border-gray-200 dark:border-gray-700;
  }

  body {
    @apply bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .drop-zone {
    @apply border-2 border-dashed border-gray-300 rounded-xl p-8 text-center transition-all duration-300 ease-in-out;
    background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .drop-zone.drag-over {
    @apply border-primary-500 bg-primary-50 shadow-lg;
    background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.1) 1px, transparent 0);
    transform: scale(1.02);
  }

  .editor-container {
    @apply h-full w-full resize-none border-0 bg-transparent p-4 font-mono text-sm leading-relaxed outline-none;
    tab-size: 2;
  }

  .preview-container {
    @apply prose prose-sm max-w-none p-4 dark:prose-invert;
  }

  .preview-container h1,
  .preview-container h2,
  .preview-container h3,
  .preview-container h4,
  .preview-container h5,
  .preview-container h6 {
    @apply scroll-mt-16;
  }

  .preview-container pre {
    @apply bg-gray-100 dark:bg-gray-800 rounded-lg;
  }

  .preview-container code {
    @apply bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm;
  }

  .preview-container blockquote {
    @apply border-l-4 border-primary-500 bg-primary-50 dark:bg-primary-900 dark:bg-opacity-20;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
