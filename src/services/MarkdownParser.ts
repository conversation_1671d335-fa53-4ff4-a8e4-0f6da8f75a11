import { marked } from 'marked';

// Configure marked for GitHub Flavored Markdown compatibility
const configureMarked = () => {
  marked.setOptions({
    gfm: true,
    breaks: false, // GitHub doesn't break on single line breaks
    pedantic: false,
    sanitize: false, // Allow HTML in markdown
    smartLists: true,
    smartypants: false, // Don't convert quotes and dashes
  });

  // Custom renderer for better GitHub compatibility
  const renderer = new marked.Renderer();

  // Override code block rendering
  renderer.code = (code: string, language: string | undefined) => {
    const lang = language || '';
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');

    return `<pre class="code-block"><code class="language-${lang}">${escapedCode}</code></pre>`;
  };

  // Override inline code rendering
  renderer.codespan = (code: string) => {
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');
    
    return `<code class="inline-code">${escapedCode}</code>`;
  };

  // Override image rendering with fallback
  renderer.image = (href: string, title: string | null, text: string) => {
    const titleAttr = title ? ` title="${title}"` : '';
    const altAttr = text ? ` alt="${text}"` : '';
    
    return `<img src="${href}"${altAttr}${titleAttr} class="markdown-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
            <div class="image-fallback" style="display:none;">
              <span class="image-fallback-text">🖼️ ${text || 'Image'}</span>
              <span class="image-fallback-url">${href}</span>
            </div>`;
  };

  // Override link rendering for security
  renderer.link = (href: string, title: string | null, text: string) => {
    const titleAttr = title ? ` title="${title}"` : '';
    const isExternal = href.startsWith('http://') || href.startsWith('https://');
    const targetAttr = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
    
    return `<a href="${href}"${titleAttr}${targetAttr} class="markdown-link">${text}</a>`;
  };

  // Override blockquote rendering
  renderer.blockquote = (quote: string) => {
    return `<blockquote class="markdown-blockquote">${quote}</blockquote>`;
  };

  // Override table rendering
  renderer.table = (header: string, body: string) => {
    return `<div class="table-wrapper">
              <table class="markdown-table">
                <thead>${header}</thead>
                <tbody>${body}</tbody>
              </table>
            </div>`;
  };

  marked.use({ renderer });
};

// Initialize marked configuration
configureMarked();

export interface ParseResult {
  html: string;
  error?: string;
  processingTime: number;
}

export class MarkdownParser {
  static parse(content: string): ParseResult {
    const startTime = performance.now();
    
    try {
      if (!content.trim()) {
        return {
          html: '<div class="empty-preview">Start editing to see preview...</div>',
          processingTime: performance.now() - startTime
        };
      }

      const html = marked(content);
      const processingTime = performance.now() - startTime;

      return {
        html,
        processingTime
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error('Markdown parsing error:', error);
      
      return {
        html: `<div class="error-preview">
                 <h3>Markdown Parsing Error</h3>
                 <p>There was an error parsing your markdown content.</p>
                 <details>
                   <summary>Error Details</summary>
                   <pre>${error instanceof Error ? error.message : String(error)}</pre>
                 </details>
               </div>`,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      };
    }
  }

  static async parseAsync(content: string): Promise<ParseResult> {
    // For Epic 1, we process on main thread
    // In Epic 3+, this would use Web Workers
    return new Promise((resolve) => {
      // Use setTimeout to prevent blocking the UI for large content
      setTimeout(() => {
        resolve(this.parse(content));
      }, 0);
    });
  }

  static isLargeContent(content: string): boolean {
    return content.length > 50000; // 50KB threshold
  }

  static estimateProcessingTime(content: string): number {
    // Rough estimate: 1ms per 1000 characters
    return Math.max(10, content.length / 1000);
  }
}
