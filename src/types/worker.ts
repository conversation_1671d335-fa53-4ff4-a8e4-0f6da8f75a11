export interface WorkerMessage {
  id: string;
  type: 'parse' | 'result' | 'error';
  payload: any;
}

export interface MarkdownParseRequest {
  content: string;
  options?: {
    gfm?: boolean;
    breaks?: boolean;
    sanitize?: boolean;
  };
}

export interface MarkdownParseResult {
  html: string;
  metadata?: {
    headings: Array<{
      level: number;
      text: string;
      id: string;
    }>;
    wordCount: number;
    readingTime: number;
  };
}
