import type { FileData } from './file';

export type ThemeMode = 'light' | 'dark' | 'system';

export type EpicLevel = 1 | 2 | 3 | 4;

export interface AppState {
  currentFile: FileData | null;
  theme: ThemeMode;
  epic: EpicLevel;
  isOffline: boolean;
}

export interface AppProps {
  initialTheme?: ThemeMode;
}

export interface EditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export type { FileData };
