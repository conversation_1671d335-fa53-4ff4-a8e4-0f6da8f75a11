export interface FileData {
  id: string;
  name: string;
  content: string;
  size: number;
  lastModified: number;
  type: string;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

export interface FileError {
  type: 'validation' | 'encoding' | 'read' | 'size' | 'empty';
  message: string;
  details?: string;
}

export interface DropZoneState {
  isDragOver: boolean;
  isProcessing: boolean;
  error?: string;
}
